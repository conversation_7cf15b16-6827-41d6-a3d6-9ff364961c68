// OEM服装相关类型定义

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  data: T
  message: string
}

// OEM服装图片
export interface OemClothingImage {
  url: string
  Key: string
}

// OEM服装信息
export interface OemClothing {
  _id?: string
  id?: string
  oem_clothing_year: string
  oem_clothing_id: string
  oem_clothing_name: string
  oem_supplier?: string
  classification?: string
  style?: string
  size?: string
  price?: number
  in_pcs?: number
  order_quantity?: number
  shipments?: number
  printed?: string
  remark?: string
  state?: string
  img?: OemClothingImage[]
  createTime?: string | Date
  lastChangeTime?: string | Date
}

// OEM服装列表响应
export interface OemClothingListResponse {
  total: number
  oemClothingList: OemClothing[]
}

// 查询参数
export interface QueryOemClothingParams {
  page?: number
  limit?: number
  oem_clothing_year?: string
  oem_clothing_years?: string[]
  oem_supplier?: string
  oem_suppliers?: string[]
  classification?: string
  classifications?: string[]
  oem_clothing_name?: string
  state?: string
}

// 创建OEM服装参数
export interface CreateOemClothingParams {
  oem_clothing_year: string
  oem_clothing_id: string
  oem_clothing_name: string
  oem_supplier?: string
  classification?: string
  style?: string
  size?: string
  price?: number
  in_pcs?: number
  order_quantity?: number
  shipments?: number
  printed?: string
  remark?: string
  state?: string
  img?: OemClothingImage[]
}

// 更新OEM服装参数
export interface UpdateOemClothingParams extends Partial<CreateOemClothingParams> {}

// 导入响应
export interface ImportResponse {
  success: boolean
  message: string
  count?: number
}
