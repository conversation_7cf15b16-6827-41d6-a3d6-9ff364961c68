// 发货信息相关类型定义

// 发货信息记录
export interface Transportation {
  _id: string
  transportation_id: string
  transportation_year: string
  date_out: string | Date
  date_arrived?: string | Date
  supplier: string
  total_package_quantity?: number
  total_pcs?: number
  weight?: number
  price?: number
  shipping_method?: string
  remark?: string
  transportation_img?: TransportationImage[]
  createTime?: string | Date
}

// 发货图片
export interface TransportationImage {
  url: string
  Key: string
}

// 发货明细
export interface TransportationDetail {
  _id?: string
  transportation_id?: string
  series_number?: number
  clothing_name: string
  package_quantity?: number
  QUP?: number
  out_pcs: number
  oem?: string
  clothing_id: string
  style?: string
  createTime?: string | Date
}

// 前端表单中使用的发货明细格式
export interface TransportationShipmentDetail {
  seriesNumber?: number
  clothingId: string
  clothingName: string
  style?: string
  packageQuantity?: number
  QUP?: number
  outPcs: number
  oem?: string
}

// 查询参数
export interface QueryParams {
  years: string[]
  suppliers: string[]
  page: number
  limit: number
}
