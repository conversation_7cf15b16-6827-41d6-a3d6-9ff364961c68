/**
 * division_work 字段拆分迁移脚本
 *
 * 此脚本将 division_work 集合中的 assign_list 和 complete_list 字段拆分到新的集合中
 *
 * 使用方法：
 * 1. 运行脚本：npx ts-node src/scripts/migrate-division-work.ts
 */

import { connect, connection } from 'mongoose'
import { config } from 'dotenv'

config()

// 旧数据结构
interface DivisionWork {
  _id: any
  division_work_id: string
  division_work_year: string
  clothing_id: string
  group_id?: string
  pcs?: number
  is_complete?: number
  assign_list: Array<AssignItem>
  complete_list: Array<CompleteItem>
}

interface WorkDetail {
  work_id: string
  work_name: string
  pcs: number | string
}

interface AssignItem {
  staff_id: string
  staff_name: string
  totalPrice: string
  work_detail: WorkDetail[]
}

interface CompleteItem {
  staff_id: string
  staff_name: string
  totalPrice: string
  work_detail: WorkDetail[]
}

interface DivisionWorkAssignDoc {
  division_work_id: any
  staff_id: string
  staff_name: string
  totalPrice: string
  work_detail: WorkDetail[]
  createTime?: Date
}

interface DivisionWorkCompleteDoc {
  division_work_id: any
  staff_id: string
  staff_name: string
  totalPrice: string
  work_detail: WorkDetail[]
  createTime?: Date
}

async function migrateData() {
  try {
    // 连接数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('数据库连接成功')

    // 获取集合
    const divisionWorkColl = connection.collection('division_work')
    const assignColl = connection.collection('division_work_assign')
    const completeColl = connection.collection('division_work_complete')

    // 获取所有旧数据
    const allData = (await divisionWorkColl.find({
      $or: [
        { assign_list: { $exists: true, $ne: [] } },
        { complete_list: { $exists: true, $ne: [] } }
      ]
    }).toArray()) as unknown as DivisionWork[]
    console.log(`找到 ${allData.length} 条 division_work 数据`)

    for (const item of allData) {
      // 处理 assign_list
      if (item.assign_list && item.assign_list.length > 0) {
        // 拆分 assign_list 到新集合
        const assignsToInsert: DivisionWorkAssignDoc[] = (item.assign_list || []).map(assign => ({
          division_work_id: item.division_work_id,
          division_work_year: item.division_work_year,
          staff_id: assign.staff_id,
          staff_name: assign.staff_name,
          totalPrice: assign.totalPrice,
          work_detail: assign.work_detail,
          createTime: new Date()
        }))

        if (assignsToInsert.length > 0) {
          await assignColl.insertMany(assignsToInsert)
          console.log(`已插入 ${assignsToInsert.length} 条 division_work_assign 数据`)
          //建立索引
          await assignColl.createIndex({ division_work_id: 1 })
          await assignColl.createIndex({ staff_id: 1 })
        }

        console.log(`已迁移 division_work: ${item.division_work_id || item._id}, assign_list 数量: ${assignsToInsert.length}`)
      }

      // 处理 complete_list
      if (item.complete_list && item.complete_list.length > 0) {
        // 拆分 complete_list 到新集合
        const completesToInsert: DivisionWorkCompleteDoc[] = (item.complete_list || []).map(complete => ({
          division_work_id: item.division_work_id,
          division_work_year: item.division_work_year,
          staff_id: complete.staff_id,
          staff_name: complete.staff_name,
          totalPrice: complete.totalPrice,
          work_detail: complete.work_detail,
          createTime: new Date()
        }))

        if (completesToInsert.length > 0) {
          await completeColl.insertMany(completesToInsert)
          console.log(`已插入 ${completesToInsert.length} 条 division_work_complete 数据`)
          //建立索引
          await completeColl.createIndex({ division_work_id: 1 })
          await completeColl.createIndex({ staff_id: 1 })
        }

        console.log(`已迁移 division_work: ${item.division_work_id || item._id}, complete_list 数量: ${completesToInsert.length}`)
      }

      // 移除 assign_list 和 complete_list 字段
      await divisionWorkColl.updateOne(
        { _id: item._id },
        { $unset: { assign_list: '', complete_list: '' } }
      )
    }

    console.log('数据迁移完成')
  } catch (error) {
    console.error('数据迁移失败:', error)
  } finally {
    await connection.close()
    console.log('数据库连接已关闭')
  }
}

migrateData()
