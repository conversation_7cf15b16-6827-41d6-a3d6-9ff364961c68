// migrate-staff-to-new.ts
// 将 staff 集合中的数据（除了 _id 字段）迁移到 staff_new 集合中

import { connect, connection, Types } from 'mongoose'
import * as dotenv from 'dotenv'

// 定义 Staff 记录的类型
interface StaffRecord extends Record<string, any> {
  _id: any
  staff_id: string
  name: string
}

dotenv.config()

async function migrateStaffToNew() {
  try {
    // 连接到数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('已连接到数据库')

    const db = connection.db
    if (!db) {
      throw new Error('数据库连接失败，db 对象为 undefined')
    }

    // 获取源集合和目标集合
    const staffCollection = db.collection('staff')
    const staffNewCollection = db.collection('staff_new')

    // 检查目标集合是否已存在
    const collections = await db.listCollections({ name: 'staff_new' }).toArray()
    if (collections.length > 0) {
      // 如果目标集合已存在，询问是否要清空
      console.log('staff_new 集合已存在')

      // 获取目标集合中的记录数
      const count = await staffNewCollection.countDocuments()
      if (count > 0) {
        console.log(`staff_new 集合中已有 ${count} 条记录`)
        console.log('正在清空 staff_new 集合...')
        await staffNewCollection.deleteMany({})
        console.log('staff_new 集合已清空')
      }
    }

    // 查找所有记录
    const allStaff = (await staffCollection.find({}).toArray()) as StaffRecord[]
    console.log(`共找到 ${allStaff.length} 条员工记录`)

    let successCount = 0
    let errorCount = 0

    // 遍历所有记录，迁移到新集合
    for (const staff of allStaff) {
      try {
        // 从原始记录中提取所有字段，除了 _id
        const { _id, ...staffDataWithoutId } = staff

        // 创建一个新的 ObjectID
        const newObjectId = new Types.ObjectId()

        // 插入到新集合
        const insertResult = await staffNewCollection.insertOne({
          _id: newObjectId,
          ...staffDataWithoutId,
        })

        if (insertResult.acknowledged) {
          console.log(
            `已将记录 ${staff.staff_id} (${staff.name}) 迁移到 staff_new 集合，新 _id: ${newObjectId}`
          )
          successCount++
        }
      } catch (error) {
        console.error(`处理记录 ${staff.staff_id} (${staff.name}) 时出错:`, error)
        errorCount++
      }
    }

    console.log('\n迁移完成:')
    console.log(`- 总记录数: ${allStaff.length}`)
    console.log(`- 成功迁移的记录数: ${successCount}`)
    console.log(`- 处理失败的记录数: ${errorCount}`)

    // 验证迁移结果
    const newCount = await staffNewCollection.countDocuments()
    console.log(`\nstaff_new 集合中现有 ${newCount} 条记录`)

    if (newCount === allStaff.length) {
      console.log('✅ 迁移成功！所有记录都已成功迁移到 staff_new 集合')
    } else {
      console.log('⚠️ 迁移可能不完整，请检查日志')
    }
  } catch (error) {
    console.error('迁移过程中出错:', error)
  } finally {
    // 关闭数据库连接
    await connection.close()
    console.log('已关闭数据库连接')
  }
}

// 执行迁移
migrateStaffToNew()
