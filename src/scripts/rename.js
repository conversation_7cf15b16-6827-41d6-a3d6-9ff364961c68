// mongoose-rename.js
const mongoose = require('mongoose')

// 连接到数据库
mongoose.connect('mongodb://localhost:27017/JY_Data', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})

const db = mongoose.connection

db.on('error', console.error.bind(console, '连接错误:'))
db.once('open', async function () {
  console.log('已连接到数据库')

  try {
    // 执行更新操作
    const result = await mongoose.connection.db
      .collection('fabric')
      .updateMany({}, { $rename: { auxiliary_cloth: 'remark' } })

    console.log(`已更新 ${result.modifiedCount} 个文档`)
  } catch (err) {
    console.error('发生错误:', err)
  } finally {
    // 关闭连接
    mongoose.connection.close()
    console.log('已关闭数据库连接')
  }
})
