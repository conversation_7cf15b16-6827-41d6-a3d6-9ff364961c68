/**
 * transportation detail 字段拆分迁移脚本
 *
 * 此脚本将 transportation 集合中的 detail 字段拆分到新的 transportation_detail 集合中
 *
 * 使用方法：
 * 1. 运行脚本：npx ts-node src/scripts/migrate-transportation-detail.ts
 */

import { connect, connection } from 'mongoose'
import { config } from 'dotenv'

config()

// 旧数据结构
interface Transportation {
  _id: any
  transportation_id: string
  transportation_year: string
  date_out: Date | { $date: string }
  supplier: string
  remark?: string
  detail: Array<TransportationDetail>
  total_package_quantity?: number
  total_pcs?: number
  weight?: number
  date_arrived?: Date | { $date: string }
  price?: number | null
  shipping_method?: string | null
}

interface TransportationDetail {
  series_number: number
  clothing_name: string
  package_quantity: number
  QUP: number
  out_pcs: number
  clothing_id: string
  style?: string
}

interface TransportationDetailDoc extends TransportationDetail {
  transportation_id: any
  createTime?: Date
}

async function migrateData() {
  try {
    // 连接数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('数据库连接成功')

    // 获取集合
    const transportationColl = connection.collection('transportation')
    const detailColl = connection.collection('transportation_detail')

    // 获取所有旧数据
    const allData = (await transportationColl.find({ detail: { $exists: true, $ne: [] } }).toArray()) as unknown as Transportation[]
    console.log(`找到 ${allData.length} 条 transportation 数据`)

    for (const item of allData) {
      // 移除 detail 字段
      await transportationColl.updateOne(
        { _id: item._id },
        { $unset: { detail: '' } }
      )

      // 拆分 detail 到新集合
      const detailsToInsert: TransportationDetailDoc[] = (item.detail || []).map(detail => ({
        ...detail,
        transportation_id: item._id,
        createTime: new Date()
      }))

      if (detailsToInsert.length > 0) {
        await detailColl.insertMany(detailsToInsert)
      }

      console.log(`已迁移 transportation: ${item.transportation_id || item._id}, 明细数量: ${detailsToInsert.length}`)
    }

    console.log('数据迁移完成')
  } catch (error) {
    console.error('数据迁移失败:', error)
  } finally {
    await connection.close()
    console.log('数据库连接已关闭')
  }
}

migrateData()
