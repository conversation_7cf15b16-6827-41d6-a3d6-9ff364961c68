/**
 * division_work 相关集合字段迁移脚本
 *
 * 此脚本将完成以下任务：
 * 1. 将 division_work_assign 和 division_work_complete 集合中的 division_work_id 替换成 string 类型，
 *    值为 division_work 集合中的 division_work_id
 * 2. 在 division_work_assign 和 division_work_complete 集合中增加所属年份字段，
 *    值为 division_work 集合中的 division_work_year
 * 3. 为 division_work_assign 和 division_work_complete 集合添加索引，
 *    分别对 division_work_id 和 staff_id 建立索引
 *
 * 使用方法：
 * 1. 运行脚本：npx ts-node src/scripts/migrate-division-work-fields.ts
 */

import { connect, connection, Types } from 'mongoose'
import { config } from 'dotenv'

config()

// 定义数据结构
interface DivisionWork {
  _id: Types.ObjectId
  division_work_id: string
  division_work_year: string
  clothing_id: string
  group_name?: string
  pcs?: number
  is_complete?: number
}

interface DivisionWorkAssign {
  _id: Types.ObjectId
  division_work_id: Types.ObjectId
  staff_id: string
  staff_name: string | null
  totalPrice: string
  work_detail: Array<{
    work_id: string
    work_name?: string
    pcs: number | string
  }>
  createTime?: Date
}

interface DivisionWorkComplete {
  _id: Types.ObjectId
  division_work_id: Types.ObjectId
  staff_id: string
  staff_name: string | null
  totalPrice: string
  work_detail: Array<{
    work_id: string
    work_name?: string
    pcs: number | string
  }>
  createTime?: Date
}

async function migrateData() {
  try {
    // 连接数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('数据库连接成功')

    // 获取集合
    const divisionWorkColl = connection.collection('division_work')
    const assignColl = connection.collection('division_work_assign')
    const completeColl = connection.collection('division_work_complete')

    // 1. 获取所有 division_work 数据，建立 _id 到 division_work_id 和 division_work_year 的映射
    const divisionWorks = await divisionWorkColl.find({}).toArray() as unknown as DivisionWork[]
    console.log(`找到 ${divisionWorks.length} 条 division_work 数据`)

    // 创建映射表
    const idToDivisionWorkId = new Map<string, string>()
    const idToDivisionWorkYear = new Map<string, string>()

    divisionWorks.forEach(work => {
      idToDivisionWorkId.set(work._id.toString(), work.division_work_id)
      idToDivisionWorkYear.set(work._id.toString(), work.division_work_year)
    })

    // 2. 更新 division_work_assign 集合
    const assignDocs = await assignColl.find({}).toArray() as unknown as DivisionWorkAssign[]
    console.log(`找到 ${assignDocs.length} 条 division_work_assign 数据`)

    let assignUpdatedCount = 0
    for (const doc of assignDocs) {
      const divisionWorkIdStr = doc.division_work_id.toString()
      const divisionWorkId = idToDivisionWorkId.get(divisionWorkIdStr)
      const divisionWorkYear = idToDivisionWorkYear.get(divisionWorkIdStr)

      if (divisionWorkId && divisionWorkYear) {
        // 更新文档
        await assignColl.updateOne(
          { _id: doc._id },
          {
            $set: {
              division_work_id: divisionWorkId,
              division_work_year: divisionWorkYear
            }
          }
        )
        assignUpdatedCount++
      } else {
        console.warn(`未找到对应的 division_work 数据: ${divisionWorkIdStr}`)
      }
    }
    console.log(`已更新 ${assignUpdatedCount} 条 division_work_assign 数据`)

    // 3. 更新 division_work_complete 集合
    const completeDocs = await completeColl.find({}).toArray() as unknown as DivisionWorkComplete[]
    console.log(`找到 ${completeDocs.length} 条 division_work_complete 数据`)

    let completeUpdatedCount = 0
    for (const doc of completeDocs) {
      const divisionWorkIdStr = doc.division_work_id.toString()
      const divisionWorkId = idToDivisionWorkId.get(divisionWorkIdStr)
      const divisionWorkYear = idToDivisionWorkYear.get(divisionWorkIdStr)

      if (divisionWorkId && divisionWorkYear) {
        // 更新文档
        await completeColl.updateOne(
          { _id: doc._id },
          {
            $set: {
              division_work_id: divisionWorkId,
              division_work_year: divisionWorkYear
            }
          }
        )
        completeUpdatedCount++
      } else {
        console.warn(`未找到对应的 division_work 数据: ${divisionWorkIdStr}`)
      }
    }
    console.log(`已更新 ${completeUpdatedCount} 条 division_work_complete 数据`)

    // 4. 为 division_work_assign 集合创建索引
    await assignColl.createIndex({ division_work_id: 1 })
    await assignColl.createIndex({ staff_id: 1 })
    console.log('已为 division_work_assign 集合创建索引')

    // 5. 为 division_work_complete 集合创建索引
    await completeColl.createIndex({ division_work_id: 1 })
    await completeColl.createIndex({ staff_id: 1 })
    console.log('已为 division_work_complete 集合创建索引')

    console.log('数据迁移完成')
  } catch (error) {
    console.error('数据迁移失败:', error)
  } finally {
    await connection.close()
    console.log('数据库连接已关闭')
  }
}

// 执行迁移
migrateData()
