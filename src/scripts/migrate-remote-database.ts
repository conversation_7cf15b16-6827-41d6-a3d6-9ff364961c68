/**
 * 远程MongoDB数据库迁移脚本
 *
 * 此脚本将完成以下任务：
 * 1. 连接到远程MongoDB服务器
 * 2. 创建新的JYNewData数据库
 * 3. 复制JY_Data的所有数据到JYNewData
 * 4. 根据数据库修改记录调整JYNewData的数据结构：
 *    - 将fabric表中的auxiliary_cloth字段重命名为remark
 *    - 将fabric_warehouse表中的detail字段拆分到新的fabric_warehouse_detail表中
 *    - 将staff表中的所有记录迁移到新的staff_new表中,并删除staff表,再将staff_new表改名为staff
 *    - 将oem_clothing_incoming表中的detail字段拆分到新的oem_clothing_incoming_detail表中
 *    - 将transportation表中的detail字段拆分到新的transportation_detail表中
 *    - 将division_work表中的assign_list和complete_list字段拆分到新的division_work_assign和division_work_complete表中
 *    - 将division_work表中的group_id字段重命名为group_name
 * 5. 根据项目，给每个表建立索引
 * 使用方法：
 * 1. 运行脚本：npx ts-node src/scripts/migrate-remote-database.ts
 */

import { MongoClient, Db } from 'mongodb'
import * as dotenv from 'dotenv'

// 加载环境变量
dotenv.config()

// 从环境变量中获取远程MongoDB连接信息
const REMOTE_HOST = process.env.REMOTE_HOST || 'localhost'
const REMOTE_PORT = process.env.REMOTE_PORT || '27017'
const REMOTE_USER = process.env.REMOTE_USER || ''
const REMOTE_PASSWORD = process.env.REMOTE_PASSWORD || ''
const REMOTE_AUTH_DB = process.env.REMOTE_AUTH_DB || 'admin'

// 从环境变量中获取源数据库和目标数据库
const SOURCE_DB = process.env.SOURCE_DB || 'JY_Data'
const TARGET_DB = process.env.TARGET_DB || 'JYNewData'

// 从环境变量中获取本地MongoDB连接信息
const LOCAL_HOST = process.env.LOCAL_HOST || 'localhost'
const LOCAL_PORT = process.env.LOCAL_PORT || '27017'

// 表名映射（远程数据库表名 -> 本地数据库表名）
const TABLE_MAPPING: Record<string, string> = {
  t_clothing: 'clothing',
  t_division_work: 'division_work',
  t_fabric: 'fabric',
  t_fabric_group: 'fabric_group',
  t_fabric_warehouse: 'fabric_warehouse',
  t_freight: 'freight',
  t_oem_clothing: 'oem_clothing',
  t_oem_clothing_incoming: 'oem_clothing_incoming',
  t_staff: 'staff',
  t_transportation: 'transportation',
  t_work: 'work',
}

// 反向表名映射（本地数据库表名 -> 远程数据库表名）
const REVERSE_TABLE_MAPPING: Record<string, string> = Object.entries(TABLE_MAPPING).reduce(
  (acc, [remote, local]) => {
    acc[local] = remote
    return acc
  },
  {} as Record<string, string>
)

// 构建连接URI
const remoteUri = `mongodb://${REMOTE_USER}:${REMOTE_PASSWORD}@${REMOTE_HOST}:${REMOTE_PORT}/${REMOTE_AUTH_DB}`
const localUri = `mongodb://${LOCAL_HOST}:${LOCAL_PORT}`

// 连接客户端
let remoteClient: MongoClient
let localClient: MongoClient

/**
 * 连接到MongoDB服务器
 */
async function connectToMongoDB() {
  try {
    console.log('正在连接到远程MongoDB服务器...')
    remoteClient = new MongoClient(remoteUri)
    await remoteClient.connect()
    console.log('远程MongoDB连接成功')

    console.log('正在连接到本地MongoDB服务器...')
    localClient = new MongoClient(localUri)
    await localClient.connect()
    console.log('本地MongoDB连接成功')

    return {
      remoteDb: remoteClient.db(SOURCE_DB),
      targetDb: remoteClient.db(TARGET_DB),
      localDb: localClient.db(SOURCE_DB),
    }
  } catch (error) {
    console.error('MongoDB连接失败:', error)
    throw error
  }
}

/**
 * 获取集合列表
 */
async function getCollections(db: Db): Promise<string[]> {
  const collections = await db.listCollections().toArray()
  return collections.map((collection: any) => collection.name)
}

/**
 * 获取本地数据库集合名称
 * 如果远程集合名称在映射表中存在，则返回对应的本地集合名称
 * 否则返回原始名称
 */
function getLocalCollectionName(remoteCollectionName: string): string {
  return TABLE_MAPPING[remoteCollectionName] || remoteCollectionName
}

/**
 * 获取远程数据库集合名称
 * 如果本地集合名称在反向映射表中存在，则返回对应的远程集合名称
 * 否则返回原始名称
 */
function getRemoteCollectionName(localCollectionName: string): string {
  return REVERSE_TABLE_MAPPING[localCollectionName] || localCollectionName
}

/**
 * 复制集合数据
 */
async function copyCollection(sourceDb: Db, targetDb: Db, remoteCollectionName: string) {
  // 获取对应的本地集合名称
  const localCollectionName = getLocalCollectionName(remoteCollectionName)

  console.log(`正在复制集合: ${remoteCollectionName} -> ${localCollectionName}`)

  // 获取源集合数据
  const sourceCollection = sourceDb.collection(remoteCollectionName)
  const targetCollection = targetDb.collection(localCollectionName)

  // 检查目标集合是否已存在，如果存在则删除
  const exists = await targetDb.listCollections({ name: localCollectionName }).hasNext()
  if (exists) {
    console.log(`目标集合 ${localCollectionName} 已存在，正在删除...`)
    await targetDb.collection(localCollectionName).drop()
  }

  // 获取源集合数据
  const data = await sourceCollection.find({}).toArray()
  console.log(`找到 ${data.length} 条记录`)

  // 如果有数据，则插入到目标集合
  if (data.length > 0) {
    await targetCollection.insertMany(data)
    console.log(`成功复制 ${data.length} 条记录到 ${localCollectionName}`)
  } else {
    console.log(`集合 ${remoteCollectionName} 没有数据，跳过`)
  }

  // 不复制索引，因为我们将在后面重新创建所有索引
  console.log(`跳过索引复制，将在数据迁移后重新创建索引`)
}

/**
 * 将fabric表中的auxiliary_cloth字段重命名为remark
 */
async function renameFabricField(db: Db) {
  const collectionName = 'fabric'
  console.log(`正在将${collectionName}表中的auxiliary_cloth字段重命名为remark...`)
  const result = await db
    .collection(collectionName)
    .updateMany({ auxiliary_cloth: { $exists: true } }, { $rename: { auxiliary_cloth: 'remark' } })
  console.log(`已更新 ${result.modifiedCount} 个文档`)
}

/**
 * 将fabric_warehouse表中的detail字段拆分到新的fabric_warehouse_detail表中
 */
async function migrateFabricWarehouseDetail(db: Db) {
  const warehouseCollectionName = 'fabric_warehouse'
  const detailCollectionName = 'fabric_warehouse_detail'

  console.log(
    `正在将${warehouseCollectionName}表中的detail字段拆分到新的${detailCollectionName}表中...`
  )

  // 获取集合
  const warehouseCollection = db.collection(warehouseCollectionName)
  const detailCollection = db.collection(detailCollectionName)

  // 获取所有旧数据
  const oldData = await warehouseCollection.find({ detail: { $exists: true } }).toArray()
  console.log(`找到 ${oldData.length} 条包含detail字段的记录`)

  // 遍历旧数据，进行迁移
  for (const item of oldData) {
    // 计算总米数
    const totalMeter = item.detail.reduce((sum: number, detail: any) => sum + detail.meter, 0)

    // 更新旧记录，添加总米数字段，移除detail字段
    await warehouseCollection.updateOne(
      { _id: item._id },
      {
        $set: { total_meter: totalMeter },
        $unset: { detail: '' },
      }
    )

    // 创建新的入库明细记录
    const detailsToInsert = item.detail.map((detail: any) => ({
      warehouse_id: item._id,
      fabric_id: detail.fabric_id,
      fabric_name: detail.fabric_name,
      meter: detail.meter,
      classification: detail.classification,
      createTime: new Date(),
    }))

    if (detailsToInsert.length > 0) {
      await detailCollection.insertMany(detailsToInsert)
    }

    console.log(`已迁移仓库记录: ${item.fabric_warehouse_id}, 明细数量: ${detailsToInsert.length}`)
  }
}

/**
 * 将staff表中的所有记录迁移到新的staff_new表中
 */
async function migrateStaffToNew(db: Db) {
  const staffCollectionName = 'staff'
  const staffNewCollectionName = 'staff_new'

  console.log(
    `正在将${staffCollectionName}表中的所有记录迁移到新的${staffNewCollectionName}表中...`
  )

  // 获取集合
  const staffCollection = db.collection(staffCollectionName)
  const staffNewCollection = db.collection(staffNewCollectionName)

  // 检查目标集合是否已存在，如果存在则删除
  const exists = await db.listCollections({ name: staffNewCollectionName }).hasNext()
  if (exists) {
    console.log(`${staffNewCollectionName}集合已存在，正在删除...`)
    await staffNewCollection.drop()
  }

  // 获取所有staff记录
  const allStaff = await staffCollection.find({}).toArray()
  console.log(`找到 ${allStaff.length} 条员工记录`)

  let successCount = 0

  // 遍历所有记录，迁移到新集合
  for (const staff of allStaff) {
    try {
      // 从原始记录中提取所有字段，除了_id
      const { _id, ...staffDataWithoutId } = staff

      // 插入到新集合
      const insertResult = await staffNewCollection.insertOne({
        ...staffDataWithoutId,
      })

      if (insertResult.acknowledged) {
        successCount++
      }
    } catch (error) {
      console.error(`处理记录 ${staff.staff_id} (${staff.name}) 时出错:`, error)
    }
  }

  console.log(`成功迁移 ${successCount} 条员工记录到${staffNewCollectionName}集合`)

  // 验证迁移结果
  const newCount = await staffNewCollection.countDocuments()
  if (newCount === allStaff.length) {
    console.log('✅ 员工数据迁移成功！')
  } else {
    console.log(
      `⚠️ 员工数据迁移可能不完整，原始记录数: ${allStaff.length}，迁移记录数: ${newCount}`
    )
  }
}

/**
 * 将oem_clothing_incoming表中的detail字段拆分到新的oem_clothing_incoming_detail表中
 */
async function migrateOemClothingIncomingDetail(db: Db) {
  const incomingCollectionName = 'oem_clothing_incoming'
  const detailCollectionName = 'oem_clothing_incoming_detail'

  console.log(
    `正在将${incomingCollectionName}表中的detail字段拆分到新的${detailCollectionName}表中...`
  )

  // 获取集合
  const incomingCollection = db.collection(incomingCollectionName)
  const detailCollection = db.collection(detailCollectionName)

  // 获取所有包含detail字段的记录
  const oldData = await incomingCollection.find({ detail: { $exists: true } }).toArray()
  console.log(`找到 ${oldData.length} 条包含detail字段的记录`)

  // 遍历旧数据，进行迁移
  for (const item of oldData) {
    // 更新旧记录，移除detail字段
    await incomingCollection.updateOne({ _id: item._id }, { $unset: { detail: '' } })

    // 创建新的入库明细记录
    const detailsToInsert = item.detail.map((detail: any) => ({
      incoming_id: item._id,
      oem_clothing_id: detail.oem_clothing_id,
      oem_clothing_name: detail.oem_clothing_name,
      in_pcs: detail.in_pcs,
      money: detail.money,
      style: detail.style,
      price: detail.price,
      createTime: new Date(),
    }))

    if (detailsToInsert.length > 0) {
      await detailCollection.insertMany(detailsToInsert)
    }

    console.log(
      `已迁移入库记录: ${item.oem_clothing_incoming_id}, 明细数量: ${detailsToInsert.length}`
    )
  }
}

/**
 * 将transportation表中的detail字段拆分到新的transportation_detail表中
 */
async function migrateTransportationDetail(db: Db) {
  const transportationCollectionName = 'transportation'
  const detailCollectionName = 'transportation_detail'

  console.log(
    `正在将${transportationCollectionName}表中的detail字段拆分到新的${detailCollectionName}表中...`
  )

  // 获取集合
  const transportationCollection = db.collection(transportationCollectionName)
  const detailCollection = db.collection(detailCollectionName)

  // 获取所有包含detail字段的记录
  const allData = await transportationCollection.find({ detail: { $exists: true } }).toArray()
  console.log(`找到 ${allData.length} 条包含detail字段的记录`)

  // 遍历旧数据，进行迁移
  for (const item of allData) {
    // 移除detail字段
    await transportationCollection.updateOne({ _id: item._id }, { $unset: { detail: '' } })

    // 拆分detail到新集合
    const detailsToInsert = item.detail.map((detail: any) => ({
      ...detail,
      transportation_id: item.transportation_id,
      createTime: new Date(),
    }))

    if (detailsToInsert.length > 0) {
      await detailCollection.insertMany(detailsToInsert)
    }

    console.log(
      `已迁移transportation记录: ${item.transportation_id || item._id}, 明细数量: ${detailsToInsert.length}`
    )
  }
}

/**
 * 将division_work表中的assign_list和complete_list字段拆分到新的division_work_assign和division_work_complete表中
 */
async function migrateDivisionWork(db: Db) {
  const divisionWorkCollectionName = 'division_work'
  const assignCollectionName = 'division_work_assign'
  const completeCollectionName = 'division_work_complete'

  console.log(
    `正在将${divisionWorkCollectionName}表中的assign_list和complete_list字段拆分到新的集合中...`
  )

  // 获取集合
  const divisionWorkCollection = db.collection(divisionWorkCollectionName)
  const assignCollection = db.collection(assignCollectionName)
  const completeCollection = db.collection(completeCollectionName)

  // 获取所有包含assign_list或complete_list字段的记录
  const allData = await divisionWorkCollection
    .find({
      $or: [
        { assign_list: { $exists: true, $ne: [] } },
        { complete_list: { $exists: true, $ne: [] } },
      ],
    })
    .toArray()

  console.log(`找到 ${allData.length} 条${divisionWorkCollectionName}数据`)

  for (const item of allData) {
    // 处理assign_list
    if (item.assign_list && item.assign_list.length > 0) {
      // 拆分assign_list到新集合
      const assignsToInsert = item.assign_list.map((assign: any) => ({
        division_work_id: item.division_work_id,
        division_work_year: item.division_work_year,
        staff_id: assign.staff_id,
        staff_name: assign.staff_name,
        totalPrice: assign.totalPrice,
        work_detail: assign.work_detail,
        createTime: new Date(),
      }))

      if (assignsToInsert.length > 0) {
        await assignCollection.insertMany(assignsToInsert)
        console.log(`已插入 ${assignsToInsert.length} 条${assignCollectionName}数据`)
      }
    }

    // 处理complete_list
    if (item.complete_list && item.complete_list.length > 0) {
      // 拆分complete_list到新集合
      const completesToInsert = item.complete_list.map((complete: any) => ({
        division_work_id: item.division_work_id,
        division_work_year: item.division_work_year,
        staff_id: complete.staff_id,
        staff_name: complete.staff_name,
        totalPrice: complete.totalPrice,
        work_detail: complete.work_detail,
        createTime: new Date(),
      }))

      if (completesToInsert.length > 0) {
        await completeCollection.insertMany(completesToInsert)
        console.log(`已插入 ${completesToInsert.length} 条${completeCollectionName}数据`)
      }
    }

    // 移除旧字段
    await divisionWorkCollection.updateOne(
      { _id: item._id },
      {
        $unset: {
          assign_list: '',
          complete_list: '',
        },
      }
    )
  }

  // 创建索引
  await assignCollection.createIndex({ division_work_id: 1 })
  await assignCollection.createIndex({ staff_id: 1 })
  await completeCollection.createIndex({ division_work_id: 1 })
  await completeCollection.createIndex({ staff_id: 1 })

  console.log(`${divisionWorkCollectionName}数据迁移完成，已创建索引`)
}

/**
 * 将division_work表中的group_id字段重命名为group_name
 */
async function renameDivisionWorkField(db: Db) {
  const collectionName = 'division_work'
  console.log(`正在将${collectionName}表中的group_id字段重命名为group_name...`)
  const result = await db
    .collection(collectionName)
    .updateMany({ group_id: { $exists: true } }, { $rename: { group_id: 'group_name' } })
  console.log(`已更新 ${result.modifiedCount} 个文档`)
}

/**
 * 比较本地数据库和远程数据库的结构
 */
async function compareDbStructure(localDb: Db, targetDb: Db) {
  console.log('正在比较本地数据库和远程数据库的结构...')

  // 获取本地数据库的集合列表
  const localCollections = await getCollections(localDb)
  console.log(`本地数据库包含 ${localCollections.length} 个集合`)

  // 获取远程数据库的集合列表
  const targetCollections = await getCollections(targetDb)
  console.log(`远程数据库包含 ${targetCollections.length} 个集合`)

  // 将本地集合名称转换为远程集合名称进行比较
  const localCollectionsConverted = localCollections.map((collection) => collection)

  // 检查本地数据库中存在但远程数据库中不存在的集合
  const missingCollections = localCollectionsConverted.filter(
    (collection) => !targetCollections.includes(collection)
  )

  if (missingCollections.length > 0) {
    console.log(`⚠️ 远程数据库中缺少以下集合: ${missingCollections.join(', ')}`)
  } else {
    console.log('✅ 远程数据库包含本地数据库的所有集合')
  }

  // 检查每个集合的字段结构
  console.log('正在比较集合的字段结构...')

  for (const localCollection of localCollections) {
    // 获取对应的远程集合名称
    const targetCollection = localCollection

    // 跳过不在远程数据库中的集合
    if (!targetCollections.includes(targetCollection)) {
      console.log(`集合 ${localCollection} -> ${targetCollection} 不存在于远程数据库，跳过比较`)
      continue
    }

    // 从每个集合中获取一条记录来比较字段结构
    const localSample = await localDb.collection(localCollection).findOne({})
    const targetSample = await targetDb.collection(targetCollection).findOne({})

    if (!localSample || !targetSample) {
      console.log(`集合 ${localCollection} -> ${targetCollection} 没有数据，跳过字段结构比较`)
      continue
    }

    // 获取字段列表（排除_id字段）
    const localFields = Object.keys(localSample)
      .filter((field) => field !== '_id')
      .sort()
    const targetFields = Object.keys(targetSample)
      .filter((field) => field !== '_id')
      .sort()

    // 比较字段列表
    const missingFields = localFields.filter((field) => !targetFields.includes(field))
    const extraFields = targetFields.filter((field) => !localFields.includes(field))

    if (missingFields.length > 0 || extraFields.length > 0) {
      console.log(`⚠️ 集合 ${localCollection} -> ${targetCollection} 的字段结构不一致:`)
      if (missingFields.length > 0) {
        console.log(`  - 远程数据库中缺少字段: ${missingFields.join(', ')}`)
      }
      if (extraFields.length > 0) {
        console.log(`  - 远程数据库中多出字段: ${extraFields.join(', ')}`)
      }
    } else {
      console.log(`✅ 集合 ${localCollection} -> ${targetCollection} 的字段结构一致`)
    }
  }
}

/**
 * 安全地创建索引的辅助函数
 * 会先检查并删除可能冲突的索引，然后再创建新索引
 * @param db 数据库连接
 * @param collectionName 集合名称
 * @param field 字段名称
 * @param indexOptions 索引选项
 * @param direction 索引方向，1表示升序，-1表示降序，默认为1
 */
async function safeCreateIndex(
  db: Db,
  collectionName: string,
  field: string,
  indexOptions: any = {},
  direction: number = 1
) {
  try {
    // 获取索引名称
    const indexName = indexOptions.name || `idx_${field}`

    // 先获取现有索引
    const existingIndexes = await db.collection(collectionName).listIndexes().toArray()

    // 检查是否有可能冲突的索引
    for (const index of existingIndexes) {
      if (index.name !== '_id_' && index.key && index.key[field] !== undefined) {
        console.log(`删除可能冲突的索引: ${index.name}`)
        try {
          await db.collection(collectionName).dropIndex(index.name)
        } catch (dropError) {
          console.error(`删除索引 ${index.name} 失败:`, dropError)
        }
      }
    }

    // 创建新索引
    await db.collection(collectionName).createIndex({ [field]: direction }, indexOptions)
    console.log(`成功创建索引: ${indexName}`)
    return true
  } catch (error) {
    console.error(`创建索引失败 (${collectionName}.${field}):`, error)
    return false
  }
}

/**
 * 为每个集合创建索引
 */
async function createIndexesForAllCollections(db: Db) {
  console.log('开始为所有集合创建索引...')

  try {
    // 用户集合索引
    await createIndexesForUsers(db)

    // 布料相关集合索引
    await createIndexesForFabric(db)
    await createIndexesForFabricWarehouse(db)
    await createIndexesForFabricWarehouseDetail(db)
    await createIndexesForFabricGroup(db)

    // 服装相关集合索引
    await createIndexesForClothing(db)
    await createIndexesForOemClothing(db)
    await createIndexesForOemClothingIncoming(db)
    await createIndexesForOemClothingIncomingDetail(db)

    // 员工相关集合索引
    await createIndexesForStaff(db)

    // 工作相关集合索引
    await createIndexesForWork(db)
    await createIndexesForDivisionWork(db)
    await createIndexesForDivisionWorkAssign(db)
    await createIndexesForDivisionWorkComplete(db)

    // 运输相关集合索引
    await createIndexesForTransportation(db)
    await createIndexesForTransportationDetail(db)

    console.log('所有集合索引创建完成')
  } catch (error) {
    console.error('创建索引过程中发生错误:', error)
    console.log('继续执行其他迁移任务...')
  }
}

/**
 * 为用户集合创建索引
 */
async function createIndexesForUsers(db: Db) {
  const collectionName = 'users'
  console.log(`正在为${collectionName}集合创建索引...`)

  // 使用安全创建索引的辅助函数
  await safeCreateIndex(db, collectionName, 'userName', { name: 'idx_userName' })
  await safeCreateIndex(db, collectionName, 'wxOpenId', { name: 'idx_wxOpenId', sparse: true })
  await safeCreateIndex(db, collectionName, 'email', { name: 'idx_email', sparse: true })

  console.log(`${collectionName}集合索引创建完成`)
}

/**
 * 为布料集合创建索引
 */
async function createIndexesForFabric(db: Db) {
  const collectionName = 'fabric'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { fabric_id: 1 },
      { name: 'idx_fabric_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ fabric_name: 1 }, { name: 'idx_fabric_name' })
    await db.collection(collectionName).createIndex({ fabric_year: 1 }, { name: 'idx_fabric_year' })
    await db.collection(collectionName).createIndex({ supplier: 1 }, { name: 'idx_supplier' })
    await db.collection(collectionName).createIndex({ classification: 1 }, { name: 'idx_classification', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ fabric_id: 1 }, { name: 'idx_fabric_id_non_unique' })
    await db.collection(collectionName).createIndex({ fabric_name: 1 }, { name: 'idx_fabric_name' })
    await db.collection(collectionName).createIndex({ fabric_year: 1 }, { name: 'idx_fabric_year' })
    await db.collection(collectionName).createIndex({ supplier: 1 }, { name: 'idx_supplier' })
    await db.collection(collectionName).createIndex({ classification: 1 }, { name: 'idx_classification', sparse: true })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为布料仓库集合创建索引
 */
async function createIndexesForFabricWarehouse(db: Db) {
  const collectionName = 'fabric_warehouse'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { fabric_warehouse_id: 1 },
      { name: 'idx_fabric_warehouse_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ fabric_warehouse_year: 1 }, { name: 'idx_fabric_warehouse_year' })
    await db.collection(collectionName).createIndex({ supplier: 1 }, { name: 'idx_supplier' })
    await db.collection(collectionName).createIndex({ date_in: -1 }, { name: 'idx_date_in' })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ fabric_warehouse_id: 1 }, { name: 'idx_fabric_warehouse_id_non_unique' })
    await db.collection(collectionName).createIndex({ fabric_warehouse_year: 1 }, { name: 'idx_fabric_warehouse_year' })
    await db.collection(collectionName).createIndex({ supplier: 1 }, { name: 'idx_supplier' })
    await db.collection(collectionName).createIndex({ date_in: -1 }, { name: 'idx_date_in' })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为布料仓库明细集合创建索引
 */
async function createIndexesForFabricWarehouseDetail(db: Db) {
  const collectionName = 'fabric_warehouse_detail'
  console.log(`正在为${collectionName}集合创建索引...`)

  await db.collection(collectionName).createIndex({ warehouse_id: 1 }, { name: 'idx_warehouse_id' })
  await db.collection(collectionName).createIndex({ fabric_id: 1 }, { name: 'idx_fabric_id' })
  await db.collection(collectionName).createIndex({ fabric_name: 1 }, { name: 'idx_fabric_name' })
  await db.collection(collectionName).createIndex({ classification: 1 }, { name: 'idx_classification', sparse: true })

  console.log(`${collectionName}集合索引创建完成`)
}

/**
 * 为布料组集合创建索引
 */
async function createIndexesForFabricGroup(db: Db) {
  const collectionName = 'fabric_group'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { fabric_group_id: 1 },
      { name: 'idx_fabric_group_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ fabric_group_year: 1 }, { name: 'idx_fabric_group_year' })
    await db.collection(collectionName).createIndex({ supplier: 1 }, { name: 'idx_supplier' })
    await db.collection(collectionName).createIndex({ state: 1 }, { name: 'idx_state', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ fabric_group_id: 1 }, { name: 'idx_fabric_group_id_non_unique' })
    await db.collection(collectionName).createIndex({ fabric_group_year: 1 }, { name: 'idx_fabric_group_year' })
    await db.collection(collectionName).createIndex({ supplier: 1 }, { name: 'idx_supplier' })
    await db.collection(collectionName).createIndex({ state: 1 }, { name: 'idx_state', sparse: true })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为服装集合创建索引
 */
async function createIndexesForClothing(db: Db) {
  const collectionName = 'clothing'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { clothing_id: 1 },
      { name: 'idx_clothing_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ clothing_name: 1 }, { name: 'idx_clothing_name' })
    await db.collection(collectionName).createIndex({ clothing_year: 1 }, { name: 'idx_clothing_year' })
    await db.collection(collectionName).createIndex({ fabric_group_id: 1 }, { name: 'idx_fabric_group_id', sparse: true })
    await db.collection(collectionName).createIndex({ state: 1 }, { name: 'idx_state', sparse: true })
    await db.collection(collectionName).createIndex({ style: 1 }, { name: 'idx_style', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ clothing_id: 1 }, { name: 'idx_clothing_id_non_unique' })
    await db.collection(collectionName).createIndex({ clothing_name: 1 }, { name: 'idx_clothing_name' })
    await db.collection(collectionName).createIndex({ clothing_year: 1 }, { name: 'idx_clothing_year' })
    await db.collection(collectionName).createIndex({ fabric_group_id: 1 }, { name: 'idx_fabric_group_id', sparse: true })
    await db.collection(collectionName).createIndex({ state: 1 }, { name: 'idx_state', sparse: true })
    await db.collection(collectionName).createIndex({ style: 1 }, { name: 'idx_style', sparse: true })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为OEM服装集合创建索引
 */
async function createIndexesForOemClothing(db: Db) {
  const collectionName = 'oem_clothing'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 使用安全创建索引的辅助函数
    await safeCreateIndex(
      db,
      collectionName,
      'oem_clothing_id',
      { name: 'idx_oem_clothing_id', unique: true, sparse: true }
    )

    // 创建其他索引
    await safeCreateIndex(db, collectionName, 'oem_clothing_name', { name: 'idx_oem_clothing_name' })
    await safeCreateIndex(db, collectionName, 'oem_clothing_year', { name: 'idx_oem_clothing_year' })
    await safeCreateIndex(db, collectionName, 'oem_supplier', { name: 'idx_oem_supplier', sparse: true })
    await safeCreateIndex(db, collectionName, 'state', { name: 'idx_state', sparse: true })
    await safeCreateIndex(db, collectionName, 'style', { name: 'idx_style', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    console.log(`跳过${collectionName}集合的索引创建`)
  }
}

/**
 * 为OEM服装入库集合创建索引
 */
async function createIndexesForOemClothingIncoming(db: Db) {
  const collectionName = 'oem_clothing_incoming'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 使用安全创建索引的辅助函数
    await safeCreateIndex(
      db,
      collectionName,
      'oem_clothing_incoming_id',
      { name: 'idx_oem_clothing_incoming_id', unique: true, sparse: true }
    )

    await safeCreateIndex(db, collectionName, 'oem_clothing_incoming_year', { name: 'idx_oem_clothing_incoming_year' })
    await safeCreateIndex(db, collectionName, 'supplier', { name: 'idx_supplier' })

    // 使用改进后的safeCreateIndex函数创建降序索引
    await safeCreateIndex(db, collectionName, 'date_in', { name: 'idx_date_in' }, -1)

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    console.log(`跳过${collectionName}集合的索引创建`)
  }
}

/**
 * 为OEM服装入库明细集合创建索引
 */
async function createIndexesForOemClothingIncomingDetail(db: Db) {
  const collectionName = 'oem_clothing_incoming_detail'
  console.log(`正在为${collectionName}集合创建索引...`)

  await db.collection(collectionName).createIndex({ incoming_id: 1 }, { name: 'idx_incoming_id' })
  await db.collection(collectionName).createIndex({ oem_clothing_id: 1 }, { name: 'idx_oem_clothing_id' })
  await db.collection(collectionName).createIndex({ oem_clothing_name: 1 }, { name: 'idx_oem_clothing_name' })
  await db.collection(collectionName).createIndex({ style: 1 }, { name: 'idx_style', sparse: true })

  console.log(`${collectionName}集合索引创建完成`)
}

/**
 * 为员工集合创建索引
 */
async function createIndexesForStaff(db: Db) {
  const collectionName = 'staff'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { staff_id: 1 },
      { name: 'idx_staff_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ name: 1 }, { name: 'idx_name' })
    await db.collection(collectionName).createIndex({ pinyin: 1 }, { name: 'idx_pinyin', sparse: true })
    await db.collection(collectionName).createIndex({ post: 1 }, { name: 'idx_post', sparse: true })
    await db.collection(collectionName).createIndex({ floor: 1 }, { name: 'idx_floor', sparse: true })
    await db.collection(collectionName).createIndex({ clearing: 1 }, { name: 'idx_clearing' })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ staff_id: 1 }, { name: 'idx_staff_id_non_unique' })
    await db.collection(collectionName).createIndex({ name: 1 }, { name: 'idx_name' })
    await db.collection(collectionName).createIndex({ pinyin: 1 }, { name: 'idx_pinyin', sparse: true })
    await db.collection(collectionName).createIndex({ post: 1 }, { name: 'idx_post', sparse: true })
    await db.collection(collectionName).createIndex({ floor: 1 }, { name: 'idx_floor', sparse: true })
    await db.collection(collectionName).createIndex({ clearing: 1 }, { name: 'idx_clearing' })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为工作集合创建索引
 */
async function createIndexesForWork(db: Db) {
  const collectionName = 'work'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { work_id: 1 },
      { name: 'idx_work_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ work_name: 1 }, { name: 'idx_work_name' })
    await db.collection(collectionName).createIndex({ work_price: 1 }, { name: 'idx_work_price' })
    await db.collection(collectionName).createIndex({ 'yearly_prices.year': 1 }, { name: 'idx_yearly_prices_year', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ work_id: 1 }, { name: 'idx_work_id_non_unique' })
    await db.collection(collectionName).createIndex({ work_name: 1 }, { name: 'idx_work_name' })
    await db.collection(collectionName).createIndex({ work_price: 1 }, { name: 'idx_work_price' })
    await db.collection(collectionName).createIndex({ 'yearly_prices.year': 1 }, { name: 'idx_yearly_prices_year', sparse: true })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为分工集合创建索引
 */
async function createIndexesForDivisionWork(db: Db) {
  const collectionName = 'division_work'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 将唯一索引改为稀疏索引，这样null值不会被包含在唯一性检查中
    await db.collection(collectionName).createIndex(
      { division_work_id: 1 },
      { name: 'idx_division_work_id', unique: true, sparse: true }
    )

    await db.collection(collectionName).createIndex({ division_work_year: 1 }, { name: 'idx_division_work_year' })
    await db.collection(collectionName).createIndex({ clothing_id: 1 }, { name: 'idx_clothing_id' })
    await db.collection(collectionName).createIndex({ group_name: 1 }, { name: 'idx_group_name', sparse: true })
    await db.collection(collectionName).createIndex({ is_complete: 1 }, { name: 'idx_is_complete' })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    // 如果创建唯一索引失败，尝试只创建非唯一索引
    console.log(`尝试为${collectionName}集合创建非唯一索引...`)

    await db.collection(collectionName).createIndex({ division_work_id: 1 }, { name: 'idx_division_work_id_non_unique' })
    await db.collection(collectionName).createIndex({ division_work_year: 1 }, { name: 'idx_division_work_year' })
    await db.collection(collectionName).createIndex({ clothing_id: 1 }, { name: 'idx_clothing_id' })
    await db.collection(collectionName).createIndex({ group_name: 1 }, { name: 'idx_group_name', sparse: true })
    await db.collection(collectionName).createIndex({ is_complete: 1 }, { name: 'idx_is_complete' })

    console.log(`${collectionName}集合非唯一索引创建完成`)
  }
}

/**
 * 为分工分配集合创建索引
 */
async function createIndexesForDivisionWorkAssign(db: Db) {
  const collectionName = 'division_work_assign'
  console.log(`正在为${collectionName}集合创建索引...`)

  await db.collection(collectionName).createIndex({ division_work_id: 1 }, { name: 'idx_division_work_id' })
  await db.collection(collectionName).createIndex({ division_work_year: 1 }, { name: 'idx_division_work_year' })
  await db.collection(collectionName).createIndex({ staff_id: 1 }, { name: 'idx_staff_id' })
  await db.collection(collectionName).createIndex({ staff_name: 1 }, { name: 'idx_staff_name' })
  await db.collection(collectionName).createIndex({ 'work_detail.work_id': 1 }, { name: 'idx_work_detail_work_id', sparse: true })

  console.log(`${collectionName}集合索引创建完成`)
}

/**
 * 为分工完成集合创建索引
 */
async function createIndexesForDivisionWorkComplete(db: Db) {
  const collectionName = 'division_work_complete'
  console.log(`正在为${collectionName}集合创建索引...`)

  await db.collection(collectionName).createIndex({ division_work_id: 1 }, { name: 'idx_division_work_id' })
  await db.collection(collectionName).createIndex({ division_work_year: 1 }, { name: 'idx_division_work_year' })
  await db.collection(collectionName).createIndex({ staff_id: 1 }, { name: 'idx_staff_id' })
  await db.collection(collectionName).createIndex({ staff_name: 1 }, { name: 'idx_staff_name' })
  await db.collection(collectionName).createIndex({ 'work_detail.work_id': 1 }, { name: 'idx_work_detail_work_id', sparse: true })
  await db.collection(collectionName).createIndex({ createTime: -1 }, { name: 'idx_createTime' })

  console.log(`${collectionName}集合索引创建完成`)
}

/**
 * 为发货信息集合创建索引
 */
async function createIndexesForTransportation(db: Db) {
  const collectionName = 'transportation'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 使用安全创建索引的辅助函数
    await safeCreateIndex(
      db,
      collectionName,
      'transportation_id',
      { name: 'idx_transportation_id', unique: true, sparse: true }
    )

    await safeCreateIndex(db, collectionName, 'transportation_year', { name: 'idx_transportation_year' })
    await safeCreateIndex(db, collectionName, 'supplier', { name: 'idx_supplier' })

    // 使用改进后的safeCreateIndex函数创建降序索引
    await safeCreateIndex(db, collectionName, 'date_out', { name: 'idx_date_out' }, -1)
    await safeCreateIndex(db, collectionName, 'date_arrived', { name: 'idx_date_arrived', sparse: true }, -1)

    await safeCreateIndex(db, collectionName, 'shipping_method', { name: 'idx_shipping_method', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    console.log(`跳过${collectionName}集合的索引创建`)
  }
}

/**
 * 为发货明细集合创建索引
 */
async function createIndexesForTransportationDetail(db: Db) {
  const collectionName = 'transportation_detail'
  console.log(`正在为${collectionName}集合创建索引...`)

  try {
    // 使用安全创建索引的辅助函数
    await safeCreateIndex(db, collectionName, 'transportation_id', { name: 'idx_transportation_id' })
    await safeCreateIndex(db, collectionName, 'clothing_id', { name: 'idx_clothing_id' })
    await safeCreateIndex(db, collectionName, 'clothing_name', { name: 'idx_clothing_name' })
    await safeCreateIndex(db, collectionName, 'style', { name: 'idx_style', sparse: true })
    await safeCreateIndex(db, collectionName, 'series_number', { name: 'idx_series_number', sparse: true })

    console.log(`${collectionName}集合索引创建完成`)
  } catch (error) {
    console.error(`创建${collectionName}集合索引失败:`, error)
    console.log(`跳过${collectionName}集合的索引创建`)
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    // 连接到MongoDB
    const { remoteDb, targetDb, localDb } = await connectToMongoDB()

    // 获取源数据库的所有集合
    const collections = await getCollections(remoteDb)
    console.log(`源数据库包含以下集合: ${collections.join(', ')}`)

    // 复制所有集合
    for (const collection of collections) {
      try {
        await copyCollection(remoteDb, targetDb, collection)
      } catch (error) {
        console.error(`复制集合 ${collection} 失败:`, error)
        console.log(`继续复制其他集合...`)
      }
    }

    console.log('所有集合复制完成')

    // 根据数据库修改记录调整数据结构
    console.log('开始调整数据结构...')

    try {
      // 1. 将fabric表中的auxiliary_cloth字段重命名为remark
      await renameFabricField(targetDb)
    } catch (error) {
      console.error('重命名fabric表字段失败:', error)
    }

    try {
      // 2. 将fabric_warehouse表中的detail字段拆分到新的fabric_warehouse_detail表中
      await migrateFabricWarehouseDetail(targetDb)
    } catch (error) {
      console.error('拆分fabric_warehouse表失败:', error)
    }

    try {
      // 3. 将staff表中的所有记录迁移到新的staff_new表中，然后删除staff表，再将staff_new表改名为staff
      await migrateStaffToNew(targetDb)
      await targetDb.collection('staff').drop()
      await targetDb.collection('staff_new').rename('staff')
    } catch (error) {
      console.error('迁移staff表失败:', error)
    }

    try {
      // 4. 将oem_clothing_incoming表中的detail字段拆分到新的oem_clothing_incoming_detail表中
      await migrateOemClothingIncomingDetail(targetDb)
    } catch (error) {
      console.error('拆分oem_clothing_incoming表失败:', error)
    }

    try {
      // 5. 将transportation表中的detail字段拆分到新的transportation_detail表中
      await migrateTransportationDetail(targetDb)
    } catch (error) {
      console.error('拆分transportation表失败:', error)
    }

    try {
      // 6. 将division_work表中的assign_list和complete_list字段拆分到新的division_work_assign和division_work_complete表中
      await migrateDivisionWork(targetDb)
    } catch (error) {
      console.error('拆分division_work表失败:', error)
    }

    try {
      // 7. 将division_work表中的group_id字段重命名为group_name
      await renameDivisionWorkField(targetDb)
    } catch (error) {
      console.error('重命名division_work表字段失败:', error)
    }

    console.log('数据结构调整完成')

    // 8. 根据项目，给每个表建立索引
    try {
      await createIndexesForAllCollections(targetDb)
    } catch (error) {
      console.error('创建索引失败:', error)
    }

    // 比较本地数据库和远程数据库的结构，确保一致
    try {
      await compareDbStructure(localDb, targetDb)
    } catch (error) {
      console.error('比较数据库结构失败:', error)
    }

    console.log('数据库迁移成功完成!')
  } catch (error) {
    console.error('数据库迁移失败:', error)
  } finally {
    // 关闭连接
    if (remoteClient) {
      await remoteClient.close()
      console.log('远程MongoDB连接已关闭')
    }
    if (localClient) {
      await localClient.close()
      console.log('本地MongoDB连接已关闭')
    }
  }
}

// 执行主函数
main()
