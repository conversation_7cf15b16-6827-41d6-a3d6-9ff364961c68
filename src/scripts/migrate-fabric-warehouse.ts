/**
 * 布料仓库数据迁移脚本
 *
 * 此脚本用于将 fabric_warehouse 表中的 detail 字段拆分到新的 fabric_warehouse_detail 表中
 *
 * 使用方法：
 * 1. 确保已经创建了新的 fabric_warehouse_detail 表
 * 2. 运行脚本：npx ts-node src/scripts/migrate-fabric-warehouse.ts
 */

import { connect, connection } from 'mongoose'
import { config } from 'dotenv'

// 加载环境变量
config()

// 定义旧的数据结构
interface OldFabricWarehouse {
  _id: any
  fabric_warehouse_id: string
  fabric_warehouse_year: string
  date_in: Date
  supplier: string
  remark?: string
  detail: Array<{
    fabric_name: string
    meter: number
    fabric_id: string
    classification?: string
  }>
  createTime?: Date
}

// 定义新的数据结构
interface NewFabricWarehouse {
  _id: any
  fabric_warehouse_id: string
  fabric_warehouse_year: string
  date_in: Date
  supplier: string
  remark?: string
  total_meter: number
  createTime?: Date
}

interface FabricWarehouseDetail {
  warehouse_id: any
  fabric_id: string
  fabric_name: string
  meter: number
  classification?: string
  remark?: string
  createTime?: Date
}

async function migrateData() {
  try {
    // 连接数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('数据库连接成功')

    // 获取集合
    const oldCollection = connection.collection('fabric_warehouse')
    const newDetailCollection = connection.collection('fabric_warehouse_detail')

    // 获取所有旧数据
    const oldData = (await oldCollection.find({}).toArray()) as unknown as OldFabricWarehouse[]
    console.log(`找到 ${oldData.length} 条旧数据`)

    // 遍历旧数据，进行迁移
    for (const item of oldData) {
      // 计算总米数
      const totalMeter = item.detail.reduce((sum, detail) => sum + detail.meter, 0)

      // 更新旧记录，添加总米数字段，移除 detail 字段
      await oldCollection.updateOne(
        { _id: item._id },
        {
          $set: { total_meter: totalMeter },
          $unset: { detail: '' },
        }
      )

      // 创建新的入库明细记录
      const detailsToInsert = item.detail.map((detail) => ({
        warehouse_id: item._id,
        fabric_id: detail.fabric_id,
        fabric_name: detail.fabric_name,
        meter: detail.meter,
        classification: detail.classification,
        createTime: new Date(),
      }))

      if (detailsToInsert.length > 0) {
        await newDetailCollection.insertMany(detailsToInsert)
      }

      console.log(
        `已迁移仓库记录: ${item.fabric_warehouse_id}, 明细数量: ${detailsToInsert.length}`
      )
    }

    console.log('数据迁移完成')
  } catch (error) {
    console.error('数据迁移失败:', error)
  } finally {
    // 关闭数据库连接
    await connection.close()
    console.log('数据库连接已关闭')
  }
}

// 执行迁移
migrateData()
