/**
 * OEM服装入库数据迁移脚本
 *
 * 此脚本用于将 oem_clothing_incoming 表中的 detail 字段拆分到新的 oem_clothing_incoming_detail 表中
 *
 * 使用方法：
 * 1. 确保已经创建了新的 oem_clothing_incoming_detail 表
 * 2. 运行脚本：npx ts-node src/scripts/migrate-oem-clothing-incoming.ts
 */

import { connect, connection } from 'mongoose'
import { config } from 'dotenv'

// 加载环境变量
config()

// 定义旧的数据结构
interface OldOemClothingIncoming {
  _id: any
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: Date
  supplier: string
  remark?: string
  detail: Array<{
    oem_clothing_name: string
    in_pcs: number
    money?: string
    oem_clothing_id: string
    style?: string
    price?: number
  }>
  createTime?: Date
}

// 定义新的数据结构
interface NewOemClothingIncoming {
  _id: any
  oem_clothing_incoming_id: string
  oem_clothing_incoming_year: string
  date_in: Date
  supplier: string
  remark?: string
  createTime?: Date
}

interface OemClothingIncomingDetail {
  incoming_id: any
  oem_clothing_id: string
  oem_clothing_name: string
  in_pcs: number
  money?: string
  style?: string
  price?: number
  remark?: string
  createTime?: Date
}

async function migrateData() {
  try {
    // 连接数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('数据库连接成功')

    // 获取集合
    const oldCollection = connection.collection('oem_clothing_incoming')
    const newDetailCollection = connection.collection('oem_clothing_incoming_detail')

    // 获取所有旧数据
    const oldData = (await oldCollection.find({}).toArray()) as unknown as OldOemClothingIncoming[]
    console.log(`找到 ${oldData.length} 条旧数据`)

    // 遍历旧数据，进行迁移
    for (const item of oldData) {
      // 更新旧记录，移除 detail 字段
      await oldCollection.updateOne(
        { _id: item._id },
        {
          $unset: { detail: '' },
        }
      )

      // 创建新的入库明细记录
      const detailsToInsert = item.detail.map((detail) => ({
        incoming_id: item._id,
        oem_clothing_id: detail.oem_clothing_id,
        oem_clothing_name: detail.oem_clothing_name,
        in_pcs: detail.in_pcs,
        money: detail.money,
        style: detail.style,
        price: detail.price,
        createTime: new Date(),
      }))

      if (detailsToInsert.length > 0) {
        await newDetailCollection.insertMany(detailsToInsert)
      }

      console.log(
        `已迁移入库记录: ${item.oem_clothing_incoming_id}, 明细数量: ${detailsToInsert.length}`
      )
    }

    console.log('数据迁移完成')
  } catch (error) {
    console.error('数据迁移失败:', error)
  } finally {
    // 关闭数据库连接
    await connection.close()
    console.log('数据库连接已关闭')
  }
}

// 执行迁移
migrateData()
