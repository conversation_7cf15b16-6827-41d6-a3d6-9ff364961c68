/**
 * division_work 字段重命名脚本
 *
 * 此脚本将 division_work 集合中的 group_id 字段重命名为 group_name
 *
 * 使用方法：
 * 1. 运行脚本：npx ts-node src/scripts/rename-division-work-field.ts
 */

import { connect, connection } from 'mongoose'
import { config } from 'dotenv'

// 加载环境变量
config()

async function renameField() {
  try {
    // 连接数据库
    const uri = process.env.DATABASE_URL || 'mongodb://localhost:27017/JY_Data'
    await connect(uri)
    console.log('数据库连接成功')

    // 获取 division_work 集合
    const divisionWorkColl = connection.collection('division_work')

    // 查询有多少条记录包含 group_id 字段
    const countWithGroupId = await divisionWorkColl.countDocuments({ group_id: { $exists: true } })
    console.log(`找到 ${countWithGroupId} 条包含 group_id 字段的记录`)

    // 执行字段重命名操作
    const result = await divisionWorkColl.updateMany(
      { group_id: { $exists: true } }, // 只更新包含 group_id 字段的文档
      { $rename: { group_id: 'group_name' } }
    )

    console.log(`字段重命名操作完成:`)
    console.log(`- 匹配的文档数: ${result.matchedCount}`)
    console.log(`- 修改的文档数: ${result.modifiedCount}`)

    // 验证结果
    const countWithGroupName = await divisionWorkColl.countDocuments({ group_name: { $exists: true } })
    console.log(`现在有 ${countWithGroupName} 条包含 group_name 字段的记录`)

    // 检查是否还有文档包含 group_id 字段
    const remainingWithGroupId = await divisionWorkColl.countDocuments({ group_id: { $exists: true } })
    console.log(`还有 ${remainingWithGroupId} 条包含 group_id 字段的记录`)

    if (remainingWithGroupId === 0 && countWithGroupName === countWithGroupId) {
      console.log('✅ 字段重命名成功！所有 group_id 字段已重命名为 group_name')
    } else {
      console.log('⚠️ 字段重命名可能不完整，请检查日志')
    }
  } catch (error) {
    console.error('字段重命名操作失败:', error)
  } finally {
    // 关闭数据库连接
    await connection.close()
    console.log('数据库连接已关闭')
  }
}

// 执行重命名操作
renameField()
