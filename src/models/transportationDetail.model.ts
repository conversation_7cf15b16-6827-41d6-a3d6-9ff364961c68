import { Schema, model, Types } from 'mongoose'

/**
 * 发货明细模型
 */
export const TransportationDetailSchema = new Schema(
  {
    transportation_id: {
      type: Schema.Types.ObjectId,
      ref: 'Transportation',
      required: true,
    },
    series_number: {
      type: Number,
      required: false,
    },
    clothing_name: {
      type: String,
      required: true,
    },
    package_quantity: {
      type: Number,
      required: false,
      default: 0,
    },
    QUP: {
      type: Number,
      required: false,
    },
    out_pcs: {
      type: Number,
      required: true,
    },
    oem: {
      type: String,
      required: false,
    },
    clothing_id: {
      type: String,
      required: true,
    },
    style: {
      type: String,
      required: false,
    },
    createTime: {
      type: Date,
      default: Date.now,
    },
  },
  {
    collection: 'transportation_detail', // 指定集合名称为 transportation_detail
    versionKey: false, // 不使用 __v 字段
    timestamps: true, // 自动管理 createdAt 和 updatedAt
  }
)

export interface TransportationDetail {
  _id?: Types.ObjectId
  id?: string
  transportation_id: any // 关联到 Transportation 的 _id
  series_number?: number
  clothing_name: string
  package_quantity?: number
  QUP?: number
  out_pcs: number
  oem?: string
  clothing_id: string
  style?: string
  createTime?: Date
}

// 模型名称为 'TransportationDetail'，与 MongooseModule.forFeature 中注册的名称保持一致
export const TransportationDetailModel = model<TransportationDetail>(
  'TransportationDetail',
  TransportationDetailSchema
)
