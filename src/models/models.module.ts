import { Module } from '@nestjs/common'
import { MongooseModule } from '@nestjs/mongoose'
import { UserSchema } from './user.model'
import { FabricSchema } from './fabric.model'
import { FabricWarehouseSchema } from './fabricWarehouse.model'
import { FabricWarehouseDetailSchema } from './fabricWarehouseDetail.model'
import { StaffSchema } from './staff.model'
import { ClothingSchema } from './clothing.model'
import { FabricGroupSchema } from './fabricGroup.model'
import { OemClothingSchema } from './oemClothing.model'
import { WorkSchema } from './work.model'
import { OemClothingIncomingSchema } from './oemClothingIncoming.model'
import { OemClothingIncomingDetailSchema } from './oemClothingIncomingDetail.model'
import { TransportationSchema } from './transportation.model'
import { TransportationDetailSchema } from './transportationDetail.model'
import { DivisionWorkSchema } from './divisionWork.model'
import { DivisionWorkAssignSchema } from './divisionWorkAssign.model'
import { DivisionWorkCompleteSchema } from './divisionWorkComplete.model'

/**
 * 模型注册模块 - 统一管理所有数据模型
 * 这样其他模块只需导入此模块，而不需要重复注册模型
 */
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'User', schema: UserSchema },
      { name: 'Fabric', schema: FabricSchema },
      { name: 'FabricWarehouse', schema: FabricWarehouseSchema },
      { name: 'FabricWarehouseDetail', schema: FabricWarehouseDetailSchema },
      { name: 'Staff', schema: StaffSchema },
      { name: 'Clothing', schema: ClothingSchema },
      { name: 'FabricGroup', schema: FabricGroupSchema },
      { name: 'OemClothing', schema: OemClothingSchema },
      { name: 'Work', schema: WorkSchema },
      { name: 'OemClothingIncoming', schema: OemClothingIncomingSchema },
      { name: 'OemClothingIncomingDetail', schema: OemClothingIncomingDetailSchema },
      { name: 'Transportation', schema: TransportationSchema },
      { name: 'TransportationDetail', schema: TransportationDetailSchema },
      { name: 'DivisionWork', schema: DivisionWorkSchema },
      { name: 'DivisionWorkAssign', schema: DivisionWorkAssignSchema },
      { name: 'DivisionWorkComplete', schema: DivisionWorkCompleteSchema },
      // 后续添加更多模型
      // { name: 'Product', schema: ProductSchema },
      // { name: 'Order', schema: OrderSchema },
    ]),
  ],
  exports: [MongooseModule],
})
export class ModelsModule {}