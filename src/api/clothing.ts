import request from '@/utils/request'
import type {
  Clothing,
  ClothingListResponse,
  QueryClothingParams,
  CreateClothingParams,
  UpdateClothingParams,
  ImportResponse,
  ApiResponse,
} from '@/types/clothing'

/**
 * 获取服装列表
 * @param params 查询参数
 * @returns 服装列表和总数
 */
export function getClothingList(params?: QueryClothingParams) {
  return request<ApiResponse<ClothingListResponse>>({
    url: '/api/clothing',
    method: 'get',
    params,
  })
}

/**
 * 获取服装详情
 * @param id 服装ID
 * @returns 服装详情
 */
export function getClothingById(id: string) {
  return request<ApiResponse<Clothing>>({
    url: `/api/clothing/${id}`,
    method: 'get',
  })
}

/**
 * 创建服装
 * @param data 服装数据
 * @returns 创建的服装
 */
export function createClothing(data: CreateClothingParams) {
  return request<ApiResponse<Clothing>>({
    url: '/api/clothing',
    method: 'post',
    data,
  })
}

/**
 * 更新服装
 * @param id 服装ID
 * @param data 更新的服装数据
 * @returns 更新后的服装
 */
export function updateClothing(id: string, data: UpdateClothingParams) {
  return request<ApiResponse<Clothing>>({
    url: `/api/clothing/${id}`,
    method: 'put',
    data,
  })
}

/**
 * 删除服装
 * @param id 服装ID
 * @returns 删除结果
 */
export function deleteClothing(id: string) {
  return request<ApiResponse<{ success: boolean; message: string }>>({
    url: `/api/clothing/${id}`,
    method: 'delete',
  })
}

/**
 * 获取服装年份选项
 * @param params 查询参数
 * @returns 年份选项列表
 */
export function getClothingYearOptions(params?: { supplier?: string; classification?: string }) {
  return request<ApiResponse<string[]>>({
    url: '/api/clothing/options/years',
    method: 'get',
    params,
  })
}

/**
 * 获取供应商选项
 * @param params 查询参数
 * @returns 供应商选项列表
 */
export function getSupplierOptions(params?: { year?: string; classification?: string }) {
  return request<ApiResponse<string[]>>({
    url: '/api/clothing/options/suppliers',
    method: 'get',
    params,
  })
}

/**
 * 获取分类选项
 * @param params 查询参数
 * @returns 分类选项列表
 */
export function getClassificationOptions(params?: { year?: string; supplier?: string }) {
  return request<ApiResponse<string[]>>({
    url: '/api/clothing/options/classifications',
    method: 'get',
    params,
  })
}

/**
 * 获取袖长、尺码、款式、口袋类型选项
 * @returns 袖长、尺码、款式、口袋类型选项列表
 */
export function getFourOptions() {
  return request<ApiResponse<string[]>>({
    url: '/api/clothing/options/four',
    method: 'get',
  })
}

/**
 * 批量导入服装数据
 * @param data 服装数据列表
 * @returns 导入结果
 */
export function importClothingBatch(data: Partial<Clothing>[]) {
  return request<ApiResponse<ImportResponse>>({
    url: '/api/clothing/import-batch',
    method: 'post',
    data,
    timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
  })
}

/**
 * 导入JSON数据
 * @param importData 包含: sheetName, data, fileName, totalRecords
 * @returns 导入结果
 */
export const importJsonData = async (importData: any) => {
  try {
    return request<ApiResponse<ImportResponse>>({
      url: '/api/clothing/import-json',
      method: 'post',
      data: importData,
      timeout: 60000, // 增加超时时间为60秒，防止大数据量超时
    })
  } catch (error) {
    console.error('导入数据时出错:', error)
    throw error
  }
}

/**
 * 根据服装名称和年份查找服装
 * @param data 包含服装名称数组、OEM服装名称数组和服装年份
 * @returns 查询到的服装和OEM服装列表
 */
export function findClothingByNames(data: { clothing_names: string[]; clothing_year: string }) {
  return request<
    ApiResponse<{
      success: boolean
      clothingList: Clothing[]
    }>
  >({
    url: '/api/clothing/find-by-names',
    method: 'post',
    data,
  })
}
