import { Module } from '@nestjs/common'
import { JwtModule } from '@nestjs/jwt'
import { ConfigModule, ConfigService } from '@nestjs/config'
import { MiniProgramController } from './mini-program.controller'
import { MiniProgramService } from './mini-program.service'
import { ModelsModule } from '../../models/models.module'
import { ClothingModule } from '../clothing/clothing.module'
import { OemClothingModule } from '../oemClothing/oem-clothing.module'
import { TransportationModule } from '../transportation/transportation.module'
import { AuthModule } from '../auth/auth.module'

@Module({
  imports: [
    ModelsModule,
    ClothingModule,
    OemClothingModule,
    TransportationModule,
    AuthModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_SECRET')
        if (!secret) {
          throw new Error('JWT_SECRET is not defined')
        }
        return {
          secret,
          signOptions: { expiresIn: '7d' },
        }
      },
    }),
  ],
  controllers: [MiniProgramController],
  providers: [MiniProgramService],
  exports: [MiniProgramService],
})
export class MiniProgramModule {}
