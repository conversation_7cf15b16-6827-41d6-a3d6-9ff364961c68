import { Injectable, UnauthorizedException, ConflictException, HttpException, HttpStatus } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model } from 'mongoose'
import { JwtService } from '@nestjs/jwt'
import { ConfigService } from '@nestjs/config'
import { User } from '../../models/user.model'
import { Clothing } from '../../models/clothing.model'
import { OemClothing } from '../../models/oemClothing.model'
import { Transportation } from '../../models/transportation.model'
import { TransportationDetail } from '../../models/transportationDetail.model'
import { MiniProgramLoginDto, GetOpenIdDto, AddUserDto } from './dto'
import * as bcrypt from 'bcrypt'
import axios from 'axios'

@Injectable()
export class MiniProgramService {
  constructor(
    @InjectModel('User') private userModel: Model<User>,
    @InjectModel('Clothing') private clothingModel: Model<Clothing>,
    @InjectModel('OemClothing') private oemClothingModel: Model<OemClothing>,
    @InjectModel('Transportation') private transportationModel: Model<Transportation>,
    @InjectModel('TransportationDetail') private transportationDetailModel: Model<TransportationDetail>,
    private jwtService: JwtService,
    private configService: ConfigService
  ) {}

  /**
   * 获取微信OpenID
   */
  async getOpenId(getOpenIdDto: GetOpenIdDto) {
    try {
      const { code } = getOpenIdDto
      const appId = this.configService.get<string>('WECHAT_APPID')
      const appSecret = this.configService.get<string>('WECHAT_SECRET')

      if (!appId || !appSecret) {
        throw new HttpException('微信配置未设置', HttpStatus.INTERNAL_SERVER_ERROR)
      }

      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${appId}&secret=${appSecret}&js_code=${code}&grant_type=authorization_code`

      const response = await axios.get(url)

      if (response.data.errcode) {
        throw new HttpException(`微信授权失败: ${response.data.errmsg}`, HttpStatus.BAD_REQUEST)
      }

      return {
        code: 200,
        data: {
          openid: response.data.openid,
          session_key: response.data.session_key
        },
        message: '获取OpenID成功'
      }
    } catch (error) {
      console.error('获取OpenID失败:', error)
      throw new HttpException('获取OpenID失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 微信小程序登录
   */
  async login(loginDto: MiniProgramLoginDto) {
    try {
      const { wxOpenId, userName, userPwd } = loginDto

      // 如果只有 wxOpenId，尝试通过 OpenID 登录
      if (wxOpenId && !userName && !userPwd) {
        const user = await this.userModel.findOne({ wxOpenId }).exec()
        if (user) {
          const token = this.generateToken(user)
          return {
            code: 200,
            data: { token },
            message: '登录成功'
          }
        } else {
          return {
            code: 404,
            message: '用户不存在，请先注册'
          }
        }
      }

      // 如果有用户名和密码，进行常规登录验证
      if (userName && userPwd) {
        const user = await this.validateUser(userName, userPwd)

        // 如果提供了 wxOpenId，更新用户的 OpenID
        if (wxOpenId && user) {
          await this.userModel.findByIdAndUpdate(user.id, { wxOpenId }).exec()
        }

        const token = this.generateToken(user)
        return {
          code: 200,
          data: { token },
          message: '登录成功'
        }
      }

      throw new UnauthorizedException('登录参数不完整')
    } catch (error) {
      console.error('登录失败:', error)
      if (error instanceof UnauthorizedException) {
        return {
          code: 401,
          message: error.message
        }
      }
      throw new HttpException('登录失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 添加新用户
   */
  async addNewUser(addUserDto: AddUserDto) {
    try {
      const { userName, userPwd, wxOpenId, wxNickName } = addUserDto

      // 检查用户是否已存在
      const existingUser = await this.userModel.findOne({
        $or: [{ userName }, { wxOpenId }]
      }).exec()

      if (existingUser) {
        throw new ConflictException('用户已存在')
      }

      // 创建新用户
      const hashedPassword = this.hashPassword(userPwd)
      const now = new Date()

      const newUser = new this.userModel({
        userName,
        userPwd: hashedPassword,
        wxOpenId,
        wxNickName,
        createTime: now,
        lastLoginTime: now
      })

      await newUser.save()

      return {
        code: 200,
        message: '用户创建成功'
      }
    } catch (error) {
      console.error('创建用户失败:', error)
      if (error instanceof ConflictException) {
        return {
          code: 409,
          message: error.message
        }
      }
      throw new HttpException('创建用户失败', HttpStatus.INTERNAL_SERVER_ERROR)
    }
  }

  /**
   * 生成JWT令牌
   */
  private generateToken(user: any) {
    const payload = {
      sub: user._id || user.id,
      userName: user.userName,
      wxOpenId: user.wxOpenId
    }
    return this.jwtService.sign(payload)
  }

  /**
   * 验证用户
   */
  private async validateUser(userName: string, password: string) {
    const user = await this.userModel.findOne({ userName }).exec()

    if (!user) {
      throw new UnauthorizedException('账号或密码错误')
    }

    if (!user.userPwd) {
      throw new UnauthorizedException('账号或密码错误')
    }

    const isPasswordValid = this.comparePassword(password, user.userPwd)

    if (!isPasswordValid) {
      throw new UnauthorizedException('账号或密码错误')
    }

    // 更新最后登录时间
    await this.userModel.findByIdAndUpdate(user._id, { lastLoginTime: new Date() }).exec()

    const { userPwd, ...result } = user.toObject()
    return result
  }

  /**
   * 密码哈希
   */
  private hashPassword(password: string): string {
    return bcrypt.hashSync(password, 10)
  }

  /**
   * 密码比较
   */
  private comparePassword(password: string, hash: string): boolean {
    return bcrypt.compareSync(password, hash)
  }
}
