import { Injectable, NotFoundException } from '@nestjs/common'
import { InjectModel } from '@nestjs/mongoose'
import { Model, Types } from 'mongoose'
import { OemClothing } from '@/models/oemClothing.model'
import { CreateOemClothingDto, UpdateOemClothingDto, QueryOemClothingDto } from './dto'

@Injectable()
export class OemClothingService {
  constructor(@InjectModel('OemClothing') private readonly oemClothingModel: Model<OemClothing>) {}

  /**
   * 获取OEM服装列表
   * @param query 查询参数
   * @returns OEM服装列表和总数
   */
  async getOemClothingList(
    query: QueryOemClothingDto
  ): Promise<{ oemClothingList: OemClothing[]; total: number }> {
    const { page = 1, limit = 10, ...filters } = query
    const skip = (page - 1) * limit
    const limitNum = Number(limit)

    console.log('后端收到的查询参数：', JSON.stringify(query))

    // 构建查询条件
    const matchConditions: any = {}

    // 处理可能的 oem_clothing_years[]、oem_suppliers[]、classifications[] 参数
    const rawQuery = query as any // 使用 any 类型绕过 TypeScript 检查
    if (rawQuery['oem_clothing_years[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['oem_clothing_years[]'] === 'string') {
        filters.oem_clothing_years = [rawQuery['oem_clothing_years[]']]
      } else if (Array.isArray(rawQuery['oem_clothing_years[]'])) {
        filters.oem_clothing_years = rawQuery['oem_clothing_years[]']
      }
    }

    if (rawQuery['oem_suppliers[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['oem_suppliers[]'] === 'string') {
        filters.oem_suppliers = [rawQuery['oem_suppliers[]']]
      } else if (Array.isArray(rawQuery['oem_suppliers[]'])) {
        filters.oem_suppliers = rawQuery['oem_suppliers[]']
      }
    }

    if (rawQuery['classifications[]']) {
      // 如果是字符串，转换为数组
      if (typeof rawQuery['classifications[]'] === 'string') {
        filters.classifications = [rawQuery['classifications[]']]
      } else if (Array.isArray(rawQuery['classifications[]'])) {
        filters.classifications = rawQuery['classifications[]']
      }
    }

    // 处理年份筛选
    if (filters.oem_clothing_year) {
      matchConditions.oem_clothing_year = filters.oem_clothing_year
    } else if (filters.oem_clothing_years && filters.oem_clothing_years.length > 0) {
      matchConditions.oem_clothing_year = { $in: filters.oem_clothing_years }
    }

    // 处理供应商筛选
    if (filters.oem_supplier) {
      matchConditions.oem_supplier = filters.oem_supplier
    } else if (filters.oem_suppliers && filters.oem_suppliers.length > 0) {
      matchConditions.oem_supplier = { $in: filters.oem_suppliers }
    }

    // 处理分类筛选
    if (filters.classification) {
      matchConditions.classification = filters.classification
    } else if (filters.classifications && filters.classifications.length > 0) {
      matchConditions.classification = { $in: filters.classifications }
    }

    // 处理服装名称筛选
    if (filters.oem_clothing_name) {
      matchConditions.oem_clothing_name = { $regex: filters.oem_clothing_name, $options: 'i' }
    }

    // 处理状态筛选
    if (filters.state) {
      matchConditions.state = filters.state
    }

    console.log('构建的查询条件：', JSON.stringify(matchConditions))

    // 使用聚合管道查询
    const aggregationPipeline: any[] = [
      { $match: matchConditions },
      { $sort: { oem_clothing_year: -1, oem_supplier: -1, oem_clothing_id: -1 } }, // 按照服装ID倒序排列
    ]

    // 获取总数的聚合管道
    const countPipeline: any[] = [...aggregationPipeline]
    countPipeline.push({ $count: 'total' })

    // 添加分页到查询管道
    aggregationPipeline.push({ $skip: skip }, { $limit: limitNum })

    console.log('最终聚合管道：', JSON.stringify(aggregationPipeline))

    // 打印每个阶段的匹配条件
    aggregationPipeline.forEach((stage, index) => {
      if (stage.$match) {
        console.log(`管道阶段 ${index} 的匹配条件:`, JSON.stringify(stage.$match))
      }
    })

    // 执行聚合查询
    const oemClothingList = await this.oemClothingModel.aggregate(aggregationPipeline as any).exec()

    // 获取总数
    const countResult = await this.oemClothingModel.aggregate(countPipeline as any).exec()
    const total = countResult.length > 0 ? countResult[0].total : 0

    console.log(`查询结果：找到 ${oemClothingList.length} 条记录，总数：${total}`)

    return { oemClothingList, total }
  }

  /**
   * 根据ID获取OEM服装
   * @param id OEM服装ID
   * @returns OEM服装
   */
  async getOemClothingById(id: string): Promise<OemClothing> {
    const oemClothing = await this.oemClothingModel.findById(id).exec()
    if (!oemClothing) {
      throw new NotFoundException(`未找到ID为 ${id} 的OEM服装`)
    }
    return oemClothing
  }

    /**
   * 根据OemClothingID获取OEM服装
   * @param OemClothingID OEM服装OemClothingID
   * @returns OEM服装
   */
  async getOemClothingByOemClothingId(oem_clothing_id: string): Promise<OemClothing> {
    const oemClothing = await this.oemClothingModel.findOne({ oem_clothing_id }).exec()
    if (!oemClothing) {
      throw new NotFoundException(`未找到OemClothingID为 ${oem_clothing_id} 的OEM服装`)
    }
    return oemClothing
  }

  /**
   * 创建OEM服装
   * @param oemClothingData OEM服装数据
   * @returns 创建的OEM服装
   */
  async createOemClothing(oemClothingData: CreateOemClothingDto): Promise<OemClothing> {
    // 设置创建时间和最后修改时间
    const now = new Date()
    const newOemClothing = new this.oemClothingModel({
      ...oemClothingData,
      createTime: now,
      lastChangeTime: now,
    })

    return await newOemClothing.save()
  }

  /**
   * 更新OEM服装
   * @param id OEM服装ID
   * @param oemClothingData 更新的OEM服装数据
   * @returns 更新后的OEM服装
   */
  async updateOemClothing(id: string, oemClothingData: UpdateOemClothingDto): Promise<OemClothing> {
    // 设置最后修改时间
    const updateData = { ...oemClothingData, lastChangeTime: new Date() }

    let updatedOemClothing: OemClothing | null = null
    try {
      updatedOemClothing = await this.oemClothingModel
        .findByIdAndUpdate(id, updateData, { new: true })
        .exec()
    } catch (error: any) {
      console.error('更新OEM服装时出错:', error)
      throw new Error(`更新OEM服装失败: ${error.message || '未知错误'}`)
    }

    if (!updatedOemClothing) {
      throw new NotFoundException(`未找到ID为 ${id} 的OEM服装`)
    }

    return updatedOemClothing
  }

  /**
   * 删除OEM服装
   * @param id OEM服装ID
   * @returns 删除结果
   */
  async deleteOemClothing(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const result = await this.oemClothingModel.findByIdAndDelete(id).exec()
      if (!result) {
        throw new NotFoundException(`未找到ID为 ${id} 的OEM服装`)
      }
      return { success: true, message: '删除OEM服装成功' }
    } catch (error: any) {
      console.error('删除OEM服装时出错:', error)
      throw new Error(`删除OEM服装失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 获取OEM服装年份选项
   * @param oem_supplier 供应商筛选
   * @param classification 分类筛选
   * @returns 年份选项列表
   */
  async getOemClothingYearOptions(
    oem_supplier?: string,
    classification?: string
  ): Promise<string[]> {
    try {
      console.log('getOemClothingYearOptions 方法接收到的参数：', { oem_supplier, classification })

      // 构建聚合管道
      const pipeline: any[] = []

      // 添加匹配条件
      const matchConditions: any = {}

      if (oem_supplier) {
        // 如果是逗号分隔的字符串，转换为数组
        if (oem_supplier.includes(',')) {
          const supplierArray = oem_supplier.split(',').filter(Boolean)
          matchConditions.oem_supplier = { $in: supplierArray }
        } else {
          matchConditions.oem_supplier = oem_supplier
        }
      }

      if (classification) {
        // 如果是逗号分隔的字符串，转换为数组
        if (classification.includes(',')) {
          const classificationArray = classification.split(',').filter(Boolean)
          matchConditions.classification = { $in: classificationArray }
        } else {
          matchConditions.classification = classification
        }
      }

      // 如果有匹配条件，添加到管道
      if (Object.keys(matchConditions).length > 0) {
        pipeline.push({ $match: matchConditions })
      }

      // 分组获取不同的年份
      pipeline.push({ $group: { _id: '$oem_clothing_year' } })

      // 投影年份字段
      pipeline.push({ $project: { _id: 0, year: '$_id' } })

      console.log('getOemClothingYearOptions 聚合管道：', JSON.stringify(pipeline))

      // 执行聚合查询
      let years: string[] = []

      try {
        if (pipeline.length > 0) {
          const result = await this.oemClothingModel.aggregate(pipeline).exec()
          years = result.map((item) => item.year).filter(Boolean)
        } else {
          // 如果没有筛选条件，直接使用 distinct 查询
          years = await this.oemClothingModel.distinct('oem_clothing_year').exec()
          years = years.filter(Boolean)
        }
      } catch (error) {
        console.error('执行年份查询时出错:', error)
        // 出错时返回空数组，避免整个请求失败
        return []
      }

      // 确保所有年份都是字符串类型
      years = years.map((year) => String(year))

      // 过滤掉无效值
      years = years.filter((year) => year !== null && year !== undefined && year !== '')

      return years.sort((a, b) => {
        try {
          // 提取年份数字进行比较
          const yearA = parseInt(a.match(/\d+/)?.[0] || '0')
          const yearB = parseInt(b.match(/\d+/)?.[0] || '0')
          return yearB - yearA // 倒序排列
        } catch (error) {
          console.error('年份排序出错:', error)
          return 0
        }
      })
    } catch (error) {
      console.error('获取年份选项时出错:', error)
      // 出错时返回空数组，避免整个请求失败
      return []
    }
  }

  /**
   * 获取供应商选项
   * @param year 年份筛选
   * @param classification 分类筛选
   * @returns 供应商选项列表
   */
  async getSupplierOptions(year?: string, classification?: string): Promise<string[]> {
    console.log('getSupplierOptions 方法接收到的参数：', { year, classification })

    // 构建聚合管道
    const pipeline: any[] = []

    // 添加匹配条件
    const matchConditions: any = {}

    if (year) {
      // 如果是逗号分隔的字符串，转换为数组
      if (year.includes(',')) {
        const yearArray = year.split(',').filter(Boolean)
        matchConditions.oem_clothing_year = { $in: yearArray }
      } else {
        matchConditions.oem_clothing_year = year
      }
    }

    if (classification) {
      // 如果是逗号分隔的字符串，转换为数组
      if (classification.includes(',')) {
        const classificationArray = classification.split(',').filter(Boolean)
        matchConditions.classification = { $in: classificationArray }
      } else {
        matchConditions.classification = classification
      }
    }

    // 如果有匹配条件，添加到管道
    if (Object.keys(matchConditions).length > 0) {
      pipeline.push({ $match: matchConditions })
    }

    // 分组获取不同的供应商
    pipeline.push({ $group: { _id: '$oem_supplier' } })

    // 过滤掉空值
    pipeline.push({ $match: { _id: { $nin: [null, ''] } } })

    // 投影供应商字段
    pipeline.push({ $project: { _id: 0, supplier: '$_id' } })

    console.log('getSupplierOptions 聚合管道：', JSON.stringify(pipeline))

    // 执行聚合查询
    let suppliers: string[] = []

    if (pipeline.length > 0) {
      const result = await this.oemClothingModel.aggregate(pipeline).exec()
      suppliers = result.map((item) => item.supplier).filter(Boolean)
    } else {
      // 如果没有筛选条件，直接使用 distinct 查询
      suppliers = await this.oemClothingModel.distinct('oem_supplier').exec()
      suppliers = suppliers.filter(Boolean)
    }

    return suppliers.sort()
  }

  /**
   * 获取分类选项
   * @param year 年份筛选
   * @param supplier 供应商筛选
   * @returns 分类选项列表
   */
  async getClassificationOptions(year?: string, supplier?: string): Promise<string[]> {
    console.log('getClassificationOptions 方法接收到的参数：', { year, supplier })

    const filter: any = {}

    if (year) {
      // 如果是逗号分隔的字符串，转换为数组
      if (year.includes(',')) {
        const yearArray = year.split(',').filter(Boolean)
        filter.oem_clothing_year = { $in: yearArray }
      } else {
        filter.oem_clothing_year = year
      }
    }

    if (supplier) {
      // 如果是逗号分隔的字符串，转换为数组
      if (supplier.includes(',')) {
        const supplierArray = supplier.split(',').filter(Boolean)
        filter.oem_supplier = { $in: supplierArray }
      } else {
        filter.oem_supplier = supplier
      }
    }

    console.log('getClassificationOptions 查询条件：', JSON.stringify(filter))

    // 使用 distinct 查询获取分类列表
    let classifications = await this.oemClothingModel.distinct('classification', filter).exec()

    // 过滤掉空值
    classifications = classifications.filter(Boolean)

    return classifications.sort()
  }

  /**
   * 获取尺码、款式选项
   * @returns 尺码、款式选项的对象
   */
  async getTwoOptions(): Promise<{
    sizes: string[]
    styles: string[]
  }> {
    // 获取当前年份
    const currentYear = new Date().getFullYear()

    // 构建最近两年的年份字符串数组，例如 ["2023年", "2024年"]
    const recentYears = [`${currentYear}年`, `${currentYear - 1}年`]

    console.log('获取最近两年的选项数据，年份范围：', recentYears)

    // 构建查询条件，只查询最近两年的数据
    const query = { oem_clothing_year: { $in: recentYears } }

    // 使用聚合管道获取两个字段的不重复值
    const [sizes, styles] = await Promise.all([
      this.oemClothingModel.distinct('size', query).exec(),
      this.oemClothingModel.distinct('style', query).exec(),
    ])

    // 过滤掉 null 和空字符串值
    const filterValues = (arr: any[]): string[] => {
      return arr
        .filter((item) => item !== null && item !== undefined && item !== '')
        .map((item) => String(item))
    }

    const result = {
      sizes: filterValues(sizes),
      styles: filterValues(styles),
    }

    console.log('获取到的选项数据：', result)

    return result
  }

  /**
   * 批量导入OEM服装
   * @param oemClothingList OEM服装数据列表
   * @returns 导入结果
   */
  async importOemClothingBatch(
    oemClothingList: Partial<OemClothing>[]
  ): Promise<{ success: boolean; count: number; message: string }> {
    if (!oemClothingList || oemClothingList.length === 0) {
      return { success: false, count: 0, message: '没有数据可导入' }
    }

    try {
      console.log(`开始导入 ${oemClothingList.length} 条OEM服装数据`)

      // 设置创建时间和最后修改时间
      const now = new Date()
      const preparedData = oemClothingList.map((item) => ({
        ...item,
        createTime: now,
        lastChangeTime: now,
      }))

      // 使用 insertMany 批量插入数据
      const result = await this.oemClothingModel.insertMany(preparedData, { ordered: false })

      console.log(`成功导入 ${result.length} 条OEM服装数据`)

      return {
        success: true,
        count: result.length,
        message: `成功导入 ${result.length} 条OEM服装数据`,
      }
    } catch (error: any) {
      console.error('批量导入OEM服装数据时出错:', error)

      // 如果是重复键错误，尝试提取已成功插入的文档数量
      if (error.code === 11000 && error.result && error.result.result) {
        const insertedCount = error.result.result.insertedCount || 0
        return {
          success: false,
          count: insertedCount,
          message: `部分数据导入成功 (${insertedCount}/${oemClothingList.length})，但有重复的OEM服装ID`,
        }
      }

      throw new Error(`导入OEM服装数据失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 导入JSON数据
   * @param importData 导入数据
   * @returns 导入结果
   */
  async importJsonData(
    importData: any
  ): Promise<{ success: boolean; count: number; message: string }> {
    try {
      const { data } = importData

      if (!data || !Array.isArray(data) || data.length === 0) {
        return { success: false, count: 0, message: '没有有效的数据可以导入' }
      }

      console.log(`准备导入 ${data.length} 条OEM服装数据`)
      console.log('导入数据示例:', data[0])

      // 转换数据格式
      const oemClothingData = data.map((item: any) => {
        // 构建OEM服装对象
        const oemClothing: Partial<OemClothing> = {
          oem_clothing_year: item.oem_clothing_year || '',
          oem_clothing_id: item.oem_clothing_id || '',
          oem_clothing_name: item.oem_clothing_name || '',
          oem_supplier: item.oem_supplier || '',
          classification: item.classification || '',
          style: item.style || '',
          size: item.size || '',
          price: item.price ? Number(item.price) : undefined,
          in_pcs: item.in_pcs ? Number(item.in_pcs) : undefined,
          order_quantity: item.order_quantity ? Number(item.order_quantity) : undefined,
          shipments: item.shipments ? Number(item.shipments) : undefined,
          printed: item.printed || '',
          remark: item.remark || '',
          state: item.state || '0',
        }

        // 过滤掉undefined值
        return Object.fromEntries(Object.entries(oemClothing).filter(([_, v]) => v !== undefined))
      })

      // 调用批量导入方法
      return await this.importOemClothingBatch(oemClothingData)
    } catch (error: any) {
      console.error('导入JSON数据时出错:', error)
      throw new Error(`导入JSON数据失败: ${error.message || '未知错误'}`)
    }
  }

  /**
   * 根据OEM服装名称和年份查找OEM服装
   * @param data 包含OEM服装名称数组和服装年份
   * @returns 查询到的OEM服装列表
   */
  async findOemClothingByNames(data: {
    oem_clothing_names: string[]
    oem_clothing_year: string
  }): Promise<{
    success: boolean
    oemClothingList: OemClothing[]
  }> {
    console.log('服务层接收到的OEM服装查询参数:', JSON.stringify(data))

    try {
      // 查询OEM服装
      let oemClothingList: OemClothing[] = []
      if (data.oem_clothing_names && data.oem_clothing_names.length > 0) {
        oemClothingList = await this.oemClothingModel
          .find({
            oem_clothing_name: { $in: data.oem_clothing_names },
            oem_clothing_year: data.oem_clothing_year + '年',
          })
          .exec()

        console.log(`找到 ${oemClothingList.length} 条OEM服装记录`)
      }

      return {
        success: true,
        oemClothingList,
      }
    } catch (error) {
      console.error('查询OEM服装失败:', error)
      throw new Error('查询OEM服装失败: ' + (error || '未知错误'))
    }
  }
}
