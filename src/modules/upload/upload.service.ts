import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import cosUtil from '../../utils/cosUtil';
import { ImageInfoDto } from './dto/upload.dto';

@Injectable()
export class UploadService {
  private readonly logger = new Logger(UploadService.name);

  constructor(private configService: ConfigService) {
    // 设置配置服务到cosUtil
    cosUtil.setConfigService(configService);
  }

  /**
   * 上传文件到腾讯云COS
   * @param file 上传的文件
   * @param folder 存储的文件夹路径
   * @returns 上传结果，包含URL和Key
   */
  async uploadFile(file: Express.Multer.File, folder: string): Promise<ImageInfoDto> {
    try {
      // 初始化COS
      cosUtil.init();

      // 创建文件名
      const originalName = file.originalname;
      const fileExt = originalName.split('.').pop() || 'jpg';
      const timestamp = new Date().getTime();
      const newFilename = `${originalName.split('.')[0]}${timestamp}.${fileExt}`;

      // 创建文件路径
      const key = `${folder}/${newFilename}`;

      // 直接使用buffer上传到腾讯云，不再使用文件路径
      const cosResult = await cosUtil.putObject({
        key,
        buffer: file.buffer, // 使用内存中的buffer而不是文件路径
      });

      // 返回结果
      return {
        url: `https://${cosResult.Location}`,
        Key: key,
      };
    } catch (error) {
      const err = error as Error;
      this.logger.error(`上传文件失败: ${err.message}`, err.stack);
      throw err;
    }
  }

  /**
   * 删除腾讯云COS中的文件
   * @param keys 要删除的文件Key列表
   * @returns 删除结果
   */
  async deleteFiles(keys: { Key: string }[]): Promise<any> {
    try {
      // 初始化COS
      cosUtil.init();

      // 删除文件
      const cosResult = await cosUtil.deleteMultipleObject(keys);

      return cosResult;
    } catch (error) {
      const err = error as Error;
      this.logger.error(`删除文件失败: ${err.message}`, err.stack);
      throw err;
    }
  }

  /**
   * 删除腾讯云COS中的单个文件
   * @param key 要删除的文件Key
   * @returns 删除结果
   */
  async deleteFile(key: string): Promise<any> {
    try {
      // 初始化COS
      cosUtil.init();

      // 删除文件
      const cosResult = await cosUtil.deleteObject(key);

      return cosResult;
    } catch (error) {
      const err = error as Error;
      this.logger.error(`删除文件失败: ${err.message}`, err.stack);
      throw err;
    }
  }
}
