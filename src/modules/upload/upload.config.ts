import { memoryStorage } from 'multer';
import { extname } from 'path';
import { Request } from 'express';

// Multer配置
export const multerConfig = {
  storage: memoryStorage(), // 使用内存存储而不是磁盘存储
  limits: {
    fileSize: 10 * 1024 * 1024, // 限制文件大小为10MB
  },
  fileFilter: (req: Request, file: Express.Multer.File, cb: Function) => {
    // 只允许上传图片
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件 (JPEG, PNG, GIF, WEBP)'), false);
    }
  },
};
