import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common'
import { ApiTags, ApiOperation, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger'
import { TransportationService } from './transportation.service'
import {
  CreateTransportationDto,
  UpdateTransportationDto,
  QueryTransportationDto,
  CreateTransportationDetailDto,
  UpdateTransportationDetailDto,
  UpdateTransportationWithDetailsDto,
} from './dto'

@ApiTags('发货信息管理')
@Controller('/transportation')
export class TransportationController {
  constructor(private readonly transportationService: TransportationService) {}

  @Post()
  @ApiOperation({ summary: '创建发货信息' })
  create(@Body() createTransportationDto: CreateTransportationDto) {
    return this.transportationService.create(createTransportationDto)
  }

  @Get()
  @ApiOperation({ summary: '获取发货信息列表' })
  findAll(@Query() queryTransportationDto: QueryTransportationDto) {
    return this.transportationService.findAll(queryTransportationDto)
  }

  @Get('year-options')
  @ApiOperation({ summary: '获取年份选项列表' })
  @ApiQuery({ name: 'supplier', required: false, description: '运输公司过滤，多个用逗号分隔' })
  async getYearOptions(@Query('supplier') suppliers: string) {
    const years = await this.transportationService.getYearOptions(suppliers)
    return { data: { years } }
  }

  @Get('supplier-options')
  @ApiOperation({ summary: '获取运输公司选项列表' })
  @ApiQuery({ name: 'year', required: false, description: '年份过滤，多个用逗号分隔' })
  async getSupplierOptions(@Query('year') years: string) {
    const suppliers = await this.transportationService.getSupplierOptions(years)
    return { data: { suppliers } }
  }

  @Get('latest-id')
  @ApiOperation({ summary: '获取最新的发货编码' })
  async getLatestTransportationId() {
    const latestId = await this.transportationService.getLatestTransportationId()
    return {
      code: 200,
      transportation_id: latestId,
      message: '获取最新发货编码成功',
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取发货信息详情' })
  @ApiParam({ name: 'id', description: '发货信息ID' })
  findOne(@Param('id') id: string) {
    return this.transportationService.findOne(id)
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新发货信息' })
  @ApiParam({ name: 'id', description: '发货信息ID' })
  update(
    @Param('id') id: string,
    @Body() updateTransportationDto: UpdateTransportationDto
  ) {
    return this.transportationService.update(id, updateTransportationDto)
  }

  @Patch(':id/with-details')
  @ApiOperation({ summary: '更新发货信息及其明细' })
  @ApiParam({ name: 'id', description: '发货信息ID' })
  @ApiBody({ type: UpdateTransportationWithDetailsDto })
  updateWithDetails(
    @Param('id') id: string,
    @Body() updateDto: UpdateTransportationWithDetailsDto
  ) {
    return this.transportationService.update(id, updateDto.transportation, updateDto.details)
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除发货信息' })
  @ApiParam({ name: 'id', description: '发货信息ID' })
  remove(@Param('id') id: string) {
    return this.transportationService.remove(id)
  }

  @Post('detail')
  @ApiOperation({ summary: '创建发货明细' })
  createDetail(@Body() createTransportationDetailDto: CreateTransportationDetailDto) {
    return this.transportationService.createDetail(createTransportationDetailDto)
  }

  @Post('detail/batch/:transportationId')
  @ApiOperation({ summary: '批量创建发货明细' })
  @ApiParam({ name: 'transportationId', description: '发货ID' })
  createDetailBatch(
    @Param('transportationId') transportationId: string,
    @Body() details: Omit<CreateTransportationDetailDto, 'transportation_id'>[]
  ) {
    return this.transportationService.createDetailBatch(transportationId, details)
  }

  @Get('detail/transportation/:transportationId')
  @ApiOperation({ summary: '获取指定发货的所有明细' })
  @ApiParam({ name: 'transportationId', description: '发货ID' })
  findAllDetailsByTransportationId(@Param('transportationId') transportationId: string) {
    return this.transportationService.findAllDetailsByTransportationId(transportationId)
  }

  @Get('detail/clothing/:clothingId')
  @ApiOperation({ summary: '获取指定服装的发货明细' })
  @ApiParam({ name: 'clothingId', description: '服装ID' })
  findAllDetailsByClothingId(@Param('clothingId') clothingId: string) {
    return this.transportationService.findAllDetailsByClothingId(clothingId)
  }

  @Get('detail/:id')
  @ApiOperation({ summary: '获取发货明细详情' })
  @ApiParam({ name: 'id', description: '发货明细ID' })
  findOneDetail(@Param('id') id: string) {
    return this.transportationService.findOneDetail(id)
  }

  @Patch('detail/:id')
  @ApiOperation({ summary: '更新发货明细' })
  @ApiParam({ name: 'id', description: '发货明细ID' })
  updateDetail(
    @Param('id') id: string,
    @Body() updateTransportationDetailDto: UpdateTransportationDetailDto
  ) {
    return this.transportationService.updateDetail(id, updateTransportationDetailDto)
  }

  @Delete('detail/:id')
  @ApiOperation({ summary: '删除发货明细' })
  @ApiParam({ name: 'id', description: '发货明细ID' })
  removeDetail(@Param('id') id: string) {
    return this.transportationService.removeDetail(id)
  }

  @Post('export-selected-details')
  @ApiOperation({ summary: '导出选中发货明细' })
  exportSelectedDetails(@Body() body: { transportationIds: string[] }) {
    return this.transportationService.exportSelectedTransportationDetails(body.transportationIds)
  }
}
