<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    :row-class-name="tableRowClassName"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="oem_clothing_years">
        <el-select
          v-model="queryParams.oem_clothing_years"
          placeholder="选择年份"
          size="small"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 120px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="供应商" prop="oem_suppliers">
        <el-select
          v-model="queryParams.oem_suppliers"
          placeholder="选择供应商"
          size="small"
          filterable
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 120px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="classifications">
        <el-select
          v-model="queryParams.classifications"
          placeholder="选择分类"
          size="small"
          filterable
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 120px"
          @change="handleClassificationChange"
        >
          <el-option
            v-for="classification in classificationOptions"
            :key="classification"
            :label="classification"
            :value="classification"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="服装名称" prop="oem_clothing_name">
        <el-input
          v-model="queryParams.oem_clothing_name"
          placeholder="请输入服装名称"
          size="small"
          clearable
          style="width: 120px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </template>

    <!-- 表格列配置 -->
    <template #table-columns>
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column
        v-for="column in columns"
        :key="column.value"
        :prop="column.value"
        :label="column.label"
        :min-width="column.width || 120"
        :show-overflow-tooltip="true"
        v-show="visibleColumns.includes(column.value)"
        align="center"
      >
        <template #default="{ row }" v-if="column.value === 'in_pcs'">
          <el-popover
            placement="top"
            :width="200"
            trigger="click"
            popper-class="in-quantity-popover"
          >
            <template #reference>
              <span class="clickable-cell" @click="handleViewInQuantityDetail(row)">
                {{ row.in_pcs }}
              </span>
            </template>
            <template #default>
              <div v-loading="detailLoading">
                <el-table :data="inQuantityDetails" stripe size="small" style="width: 100%">
                  <el-table-column prop="date_in" label="入库日期" width="100" />
                  <el-table-column prop="in_pcs" label="数量" width="60" align="right" />
                </el-table>
              </div>
            </template>
          </el-popover>
        </template>
        <template #default="{ row }" v-else-if="column.value === 'shipments'">
          <el-popover
            placement="top"
            :width="200"
            trigger="click"
            popper-class="in-quantity-popover"
          >
            <template #reference>
              <span class="clickable-cell" @click="handleViewOutQuantityDetail(row)">
                {{ row.shipments }}
              </span>
            </template>
            <template #default>
              <div v-loading="outDetailLoading">
                <el-table :data="outQuantityDetails" stripe size="small" style="width: 100%">
                  <el-table-column prop="date" label="出库日期" width="100" />
                  <el-table-column prop="quantity" label="数量" width="60" align="right" />
                </el-table>
              </div>
            </template>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <div class="table-tools">
        <el-upload
          class="upload-button"
          :action="null"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls"
          :show-file-list="false"
        >
          <el-button type="success" plain size="small">Excel导入</el-button>
        </el-upload>

        <el-button type="warning" plain size="small" @click="handleExport">Excel导出</el-button>
      </div>
    </template>
  </PageTemplate>

  <!-- OEM服装对话框 -->
  <OemClothingDialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :oem-clothing="currentOemClothing"
    @success="handleDialogSuccess"
  />

  <!-- OEM服装导入对话框 -->
  <OemClothingImport
    v-model:visible="importDialogVisible"
    :file="importFile"
    @import-success="handleImportSuccess"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入OEM服装对话框组件
import OemClothingDialog from './components/OemClothingDialog.vue'
// 导入OEM服装导入组件
import OemClothingImport from './components/OemClothingImport.vue'
// 导入API
import {
  getOemClothingList,
  deleteOemClothing,
  getOemClothingYearOptions,
  getSupplierOptions,
  getClassificationOptions,
} from '@/api/oemClothing'
import { getOemClothingIncomingDetailsByOemClothingId } from '@/api/oemClothingIncoming'
import { getTransportationDetailsByClothingId } from '@/api/transportation'
// 导入类型定义
import type {
  OemClothing,
  QueryOemClothingParams,
  OemClothingListResponse,
} from '@/types/oemClothing'
// 引入xlsx库
import * as XLSX from 'xlsx'

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<OemClothing[]>([])

// 总数
const total = ref(0)

// 入库明细相关
const detailLoading = ref(false)
const inQuantityDetails = ref<any[]>([])

// 出库明细相关
const outDetailLoading = ref(false)
const outQuantityDetails = ref<any[]>([])

// 查询参数
const queryParams = reactive<QueryOemClothingParams>({
  page: 1,
  limit: 10,
  oem_clothing_years: [],
  oem_clothing_name: '',
  oem_supplier: '',
  oem_suppliers: [],
  classification: '',
  classifications: [],
  state: '',
})

// 年份选项
const yearOptions = ref<string[]>([])
// 供应商选项
const supplierOptions = ref<string[]>([])
// 分类选项
const classificationOptions = ref<string[]>([])

// 表格列配置
const columns = [
  { label: '所属年份', value: 'oem_clothing_year', width: 100 },
  { label: '服装编号', value: 'oem_clothing_id', width: 120 },
  { label: '服装名称', value: 'oem_clothing_name', width: 120 },
  { label: '供应商', value: 'oem_supplier', width: 120 },
  { label: '分类', value: 'classification', width: 120 },
  { label: '款式', value: 'style', width: 120 },
  { label: '尺码', value: 'size', width: 120 },
  { label: '价格', value: 'price', width: 100 },
  { label: '入库数量', value: 'in_pcs', width: 100 },
  { label: '出货数量', value: 'shipments', width: 100 },
  { label: '备注', value: 'remark', width: 120 },
]
const visibleColumns = ref([
  'oem_clothing_year',
  'oem_clothing_id',
  'oem_clothing_name',
  'oem_supplier',
  'classification',
  'style',
  'size',
  'price',
  'in_pcs',
  'shipments',
  'remark',
])

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增OEM服装')
const currentOemClothing = ref<Partial<OemClothing>>({})

// 导入对话框相关
const importDialogVisible = ref(false)
const importFile = ref<File | null>(null)

// 初始化
onMounted(async () => {
  // 加载年份选项
  await loadYearOptions()

  // 加载供应商选项
  await loadSupplierOptions()

  // 加载分类选项
  await loadClassificationOptions()

  // 加载数据
  await loadData()
})

// 加载年份选项
const loadYearOptions = async () => {
  try {
    const response = await getOemClothingYearOptions()
    if (response && response.data) {
      yearOptions.value = response.data as unknown as string[]
    }
  } catch (error) {}
}

// 根据筛选条件加载年份选项
const loadYearOptionsByFilters = async (suppliers?: string[], classifications?: string[]) => {
  try {
    // 构建查询参数
    const params: any = {}

    if (suppliers && suppliers.length > 0) {
      params.supplier = suppliers.join(',')
    }

    if (classifications && classifications.length > 0) {
      params.classification = classifications.join(',')
    }

    const response = await getOemClothingYearOptions(params)
    if (response && response.data) {
      yearOptions.value = response.data as unknown as string[]
    }
  } catch (error) {}
}

// 加载供应商选项
const loadSupplierOptions = async (year?: string, classifications?: string[]) => {
  try {
    // 构建查询参数
    const params: any = {}

    if (year) {
      params.year = year
    } else if (queryParams.oem_clothing_years && queryParams.oem_clothing_years.length > 0) {
      params.year = queryParams.oem_clothing_years.join(',')
    }

    if (classifications && classifications.length > 0) {
      params.classification = classifications.join(',')
    } else if (queryParams.classifications && queryParams.classifications.length > 0) {
      params.classification = queryParams.classifications.join(',')
    }

    const response = await getSupplierOptions(params)
    if (response && response.data) {
      supplierOptions.value = response.data as unknown as string[]
    }
  } catch (error) {}
}

// 加载分类选项
const loadClassificationOptions = async (year?: string, suppliers?: string[]) => {
  try {
    // 构建查询参数
    const params: any = {}

    if (year) {
      params.year = year
    } else if (queryParams.oem_clothing_years && queryParams.oem_clothing_years.length > 0) {
      params.year = queryParams.oem_clothing_years.join(',')
    }

    if (suppliers && suppliers.length > 0) {
      params.supplier = suppliers.join(',')
    } else if (queryParams.oem_suppliers && queryParams.oem_suppliers.length > 0) {
      params.supplier = queryParams.oem_suppliers.join(',')
    }

    const response = await getClassificationOptions(params)
    if (response && response.data) {
      classificationOptions.value = response.data as unknown as string[]
    }
  } catch (error) {}
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 处理查询参数
    const processedParams = { ...queryParams }

    // 将数组参数转换为适合后端的格式
    if (processedParams.oem_clothing_years && processedParams.oem_clothing_years.length > 0) {
      ;(processedParams as any)['oem_clothing_years[]'] = processedParams.oem_clothing_years
      delete processedParams.oem_clothing_years
    }

    if (processedParams.oem_suppliers && processedParams.oem_suppliers.length > 0) {
      ;(processedParams as any)['oem_suppliers[]'] = processedParams.oem_suppliers
      delete processedParams.oem_suppliers
    }

    if (processedParams.classifications && processedParams.classifications.length > 0) {
      ;(processedParams as any)['classifications[]'] = processedParams.classifications
      delete processedParams.classifications
    }

    const response = await getOemClothingList(processedParams)

    // 检查响应格式
    if (response && response.data) {
      const listResponse = response.data as unknown as OemClothingListResponse
      tableData.value = listResponse.oemClothingList || []
      total.value = listResponse.total || 0

      // 如果没有数据，提示用户
      if (listResponse.total === 0) {
        ElMessage.info('未查询到OEM服装数据，请尝试其他筛选条件或添加新数据')
      }
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error: any) {
    ElMessage.error(error.response?.data?.message || '加载OEM服装数据失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  queryParams.page = 1
  loadData()
}

// 重置
const handleReset = () => {
  // 重置查询参数
  queryParams.page = 1
  queryParams.oem_clothing_years = []
  queryParams.oem_clothing_name = ''
  queryParams.oem_supplier = ''
  queryParams.oem_suppliers = []
  queryParams.classification = ''
  queryParams.classifications = []
  queryParams.state = ''

  // 重新加载数据
  loadData()

  // 重新加载选项
  loadYearOptions()
  loadSupplierOptions()
  loadClassificationOptions()
}

// 新增
const handleAdd = () => {
  dialogTitle.value = '新增OEM服装'
  currentOemClothing.value = {}
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row: OemClothing) => {
  dialogTitle.value = '编辑OEM服装'
  currentOemClothing.value = { ...row }
  dialogVisible.value = true
}

// 删除
const handleDelete = (row: OemClothing) => {
  ElMessageBox.confirm(`确认删除OEM服装 ${row.oem_clothing_name} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        const response = await deleteOemClothing(row._id as string)
        if (response && response.data && (response.data as any).success) {
          ElMessage.success('删除成功')
          // 重新加载数据
          loadData()
        } else {
          ElMessage.error(response?.data?.message || '删除失败')
        }
      } catch (error: any) {
        ElMessage.error(error.response?.data?.message || '删除OEM服装失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 导入 - 直接通过文件选择器触发
// 此函数已通过 Excel 导入按钮的 handleFileChange 替代

// 文件变更
const handleFileChange = async (file: any) => {
  if (file.raw) {
    // 设置文件
    importFile.value = file.raw
    // 显示导入对话框
    importDialogVisible.value = true
  }
}

// 导出
const handleExport = () => {
  // 确认是否导出当前筛选结果
  ElMessageBox.confirm(
    `确认导出${total.value > 0 ? `当前筛选的 ${total.value} 条` : '所有'}OEM服装数据吗？`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info',
    }
  )
    .then(async () => {
      try {
        loading.value = true

        // 获取所有数据（不分页）
        const exportParams = { ...queryParams, page: 1, limit: total.value || 9999 }

        // 将数组参数转换为适合后端的格式
        if (exportParams.oem_clothing_years && exportParams.oem_clothing_years.length > 0) {
          ;(exportParams as any)['oem_clothing_years[]'] = exportParams.oem_clothing_years
          delete exportParams.oem_clothing_years
        }

        if (exportParams.oem_suppliers && exportParams.oem_suppliers.length > 0) {
          ;(exportParams as any)['oem_suppliers[]'] = exportParams.oem_suppliers
          delete exportParams.oem_suppliers
        }

        if (exportParams.classifications && exportParams.classifications.length > 0) {
          ;(exportParams as any)['classifications[]'] = exportParams.classifications
          delete exportParams.classifications
        }

        const response = await getOemClothingList(exportParams)

        if (response && response.data && (response.data as any).oemClothingList) {
          const oemClothingList = (response.data as any).oemClothingList as OemClothing[]
          const exportData = oemClothingList.map((item) => {
            // 转换为适合导出的格式
            return {
              所属年份: item.oem_clothing_year || '',
              服装编号: item.oem_clothing_id || '',
              服装名称: item.oem_clothing_name || '',
              供应商: item.oem_supplier || '',
              分类: item.classification || '',
              款式: item.style || '',
              尺码: item.size || '',
              价格: item.price || '',
              入库数量: item.in_pcs || '',
              订单数量: item.order_quantity || '',
              出货数量: item.shipments || '',
              印花: item.printed || '',
              备注: item.remark || '',
              状态: item.state === '0' ? '正常' : '已删除',
              创建时间: item.createTime ? new Date(item.createTime).toLocaleString() : '',
            }
          })

          // 创建工作簿
          const wb = XLSX.utils.book_new()
          const ws = XLSX.utils.json_to_sheet(exportData)

          // 添加工作表到工作簿
          XLSX.utils.book_append_sheet(wb, ws, 'OEM服装信息')

          // 生成文件名
          const fileName = `OEM服装信息_${new Date().toISOString().split('T')[0]}.xlsx`

          // 导出Excel
          XLSX.writeFile(wb, fileName)

          ElMessage.success(`成功导出 ${exportData.length} 条数据`)
        } else {
          ElMessage.warning('没有数据可导出')
        }
      } catch (error: any) {
        ElMessage.error(error.response?.data?.message || '导出OEM服装数据失败')
      } finally {
        loading.value = false
      }
    })
    .catch(() => {
      // 用户取消导出
    })
}

// 对话框成功回调
const handleDialogSuccess = () => {
  // 重新加载数据
  loadData()
}

// 导入成功回调
const handleImportSuccess = () => {
  // 重新加载数据
  loadData()
  // 重新加载选项
  loadYearOptions()
  loadSupplierOptions()
  loadClassificationOptions()
}

// 年份变化
const handleYearChange = () => {
  // 根据年份加载供应商选项
  loadSupplierOptions(queryParams.oem_clothing_years?.join(','))

  // 根据年份加载分类选项
  loadClassificationOptions(queryParams.oem_clothing_years?.join(','))

  // 自动查询
  handleQuery()
}

// 供应商变化
const handleSupplierChange = () => {
  // 根据供应商加载分类选项
  loadClassificationOptions(undefined, queryParams.oem_suppliers)

  // 根据供应商加载年份选项
  loadYearOptionsByFilters(queryParams.oem_suppliers, queryParams.classifications)

  // 清空分类选择
  queryParams.classifications = []

  // 自动查询
  handleQuery()
}

// 分类变化
const handleClassificationChange = () => {
  // 根据分类加载年份选项
  loadYearOptionsByFilters(queryParams.oem_suppliers, queryParams.classifications)
  loadSupplierOptions()
  // 自动查询
  handleQuery()
}

// 分页大小变化
const handleSizeChange = (val: number) => {
  queryParams.limit = val
  loadData()
}

// 当前页变化
const handleCurrentChange = (val: number) => {
  queryParams.page = val
  loadData()
}

// 表格行类名
const tableRowClassName = ({ row }: { row: OemClothing }) => {
  // 如果行数据有图片，返回 has-image 类名
  if (row.img && row.img.length > 0) {
    return 'has-image'
  }
  return ''
}

// 查看入库明细
const handleViewInQuantityDetail = async (row: OemClothing) => {
  if (!row || !row.oem_clothing_id) {
    ElMessage.warning('无法获取服装信息')
    return
  }

  detailLoading.value = true

  try {
    // 使用真实 API 获取OEM服装入库明细数据
    const response = await getOemClothingIncomingDetailsByOemClothingId(row.oem_clothing_id)
    console.log('获取入库明细响应:', response)
    //将response as any
    const detailData = response as any
    // 处理响应数据
    if (detailData && Array.isArray(detailData) && detailData.length > 0) {
      // 将后端数据转换为前端显示所需的格式
      inQuantityDetails.value = detailData.map((item: any) => ({
        date_in: item.incoming.date_in
          ? new Date(item.incoming.date_in).toISOString().split('T')[0]
          : '-',
        in_pcs: item.in_pcs || 0,
      }))
    } else {
      inQuantityDetails.value = []
      ElMessage.info('没有找到入库明细数据')
    }
  } catch (error) {
    ElMessage.error('获取入库明细数据失败')
    inQuantityDetails.value = []
  } finally {
    detailLoading.value = false
  }
}

// 查看出库明细
const handleViewOutQuantityDetail = async (row: OemClothing) => {
  if (!row || !row.oem_clothing_id) {
    ElMessage.warning('无法获取服装信息')
    return
  }

  outDetailLoading.value = true

  try {
    // 使用真实 API 获取OEM服装出货明细数据
    const response = await getTransportationDetailsByClothingId(row.oem_clothing_id)
    console.log('获取出货明细响应:', response)

    // 处理响应数据
    if (response && Array.isArray(response) && response.length > 0) {
      // 过滤出OEM服装的出货记录
      const oemDetails = response.filter((item: any) => item.oem === '是')

      // 将后端数据转换为前端显示所需的格式
      outQuantityDetails.value = oemDetails.map((item: any) => ({
        date: item.transportation.date_out
          ? new Date(item.transportation.date_out).toISOString().split('T')[0]
          : '-',
        quantity: item.out_pcs || 0,
        remark: item.transportation.remark || '-',
      }))

      if (oemDetails.length === 0) {
        ElMessage.info('没有找到出货明细数据')
      }
    } else {
      outQuantityDetails.value = []
      ElMessage.info('没有找到出货明细数据')
    }
  } catch (error) {
    ElMessage.error('获取出货明细数据失败')
    outQuantityDetails.value = []
  } finally {
    outDetailLoading.value = false
  }
}
</script>

<style scoped>
.column-item {
  margin: 5px 0;
}

.table-tools {
  display: flex;
  gap: 10px;
}

.upload-button {
  display: inline-block;
}

/* 为有图片的行添加浅绿色背景 */
:deep(.has-image) {
  background-color: #d5f1c6 !important;
}

/* 可点击单元格样式 */
.clickable-cell {
  color: #333;
  cursor: pointer;
}

/* 入库明细弹窗样式 */
:deep(.in-quantity-popover) {
  padding: 0;
}

.popover-title {
  font-size: 16px;
  font-weight: bold;
  padding: 8px 12px;
  background-color: var(--el-color-primary-light-9);
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>
