<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogType === 'add' ? '新增OEM服装入库' : '编辑OEM服装入库'"
    width="50%"
    top="10vh"
    @closed="handleClose"
    :close-on-click-modal="false"
    align-center
    center
  >
    <el-form ref="formRef" :model="form" :rules="formRules" label-width="100px">
      <div class="form-row">
        <el-form-item label="入库日期" prop="stockInDate" class="form-item">
          <el-date-picker
            v-model="form.stockInDate"
            type="date"
            placeholder="请选择入库日期"
            value-format="x"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="所属年份" prop="year" class="form-item">
          <el-input-number
            v-model="yearValue"
            controls-position="right"
            @change="handleChangeYear"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="供应商" prop="supplier" class="form-item">
          <el-select
            v-model="form.supplier"
            placeholder="供应商"
            filterable
            allow-create
            style="width: 100%"
          >
            <el-option
              v-for="supplier in supplierOptions"
              :key="supplier"
              :label="supplier"
              :value="supplier"
            />
          </el-select>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" placeholder="请输入备注" />
      </el-form-item>
      <el-form-item label="入库编码" prop="code">
        <el-input disabled v-model="form.code" />
      </el-form-item>
    </el-form>
    <div class="button-row">
      <el-upload
        class="upload-demo"
        action=""
        :show-file-list="false"
        :auto-upload="false"
        :on-change="uploadChange"
      >
        <el-button type="primary">点击上传</el-button>
      </el-upload>
      <el-button type="primary" @click="getOemClothingInfo">补齐信息</el-button>
    </div>
    <el-table
      class="detail-table"
      @cell-dblclick="tableEdit"
      :data="form.details"
      table-layout="auto"
      size="default"
      style="font-size: 16px; margin-top: 20px"
      max-height="400px"
      border
      :header-cell-style="{ 'text-align': 'center' }"
      :cell-style="{ 'text-align': 'center' }"
      :summary-cell-style="{ 'text-align': 'center', 'font-weight': 'bold' }"
      show-summary
      sum-text="合计"
      :summary-method="getSummaries"
    >
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="oemClothingId" label="服装编码" width="150" />
      <el-table-column prop="oemClothingName" label="服装名称" width="150" />
      <el-table-column prop="style" label="款式" width="120" />
      <el-table-column prop="price" label="单价" width="120" />
      <el-table-column prop="quantity" label="数量" width="120" />
      <el-table-column prop="money" label="金额" />
    </el-table>
    <template #footer>
      <el-button type="danger" v-if="dialogType === 'edit'" @click="handleDelete">删除</el-button>
      <el-button type="primary" @click="handleSubmit">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import * as XLSX from 'xlsx'
import {
  getOemClothingIncomingSupplierOptions,
  getOemClothingIncomingYearOptions,
} from '@/api/oemClothingIncoming'
import { getOemClothingList } from '@/api/oemClothing'

// 定义本地类型
interface OemClothingStockInDetail {
  oemClothingId: string
  oemClothingName: string
  style: string
  price: number
  quantity: number
  money?: string
  remark?: string
}

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  type: {
    type: String as () => 'add' | 'edit',
    required: true,
  },
  currentId: {
    type: String,
    default: '',
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'delete'])

// 对话框可见性
const dialogVisible = ref(props.visible)
const dialogType = ref(props.type)

// 监听 visible 属性变化
watch(
  () => props.visible,
  (newVal) => {
    dialogVisible.value = newVal
  }
)

// 监听对话框可见性变化
watch(dialogVisible, async (newVal) => {
  emit('update:visible', newVal)

  // 当对话框打开时
  if (newVal) {
    // 如果是新增模式，获取最新年份
    if (dialogType.value === 'add') {
      await loadLatestYear()
    }

    // 加载供应商选项
    if (form.year) {
      await loadSuppliersByYear(form.year)
    }
  }
})

// 监听 type 属性变化
watch(
  () => props.type,
  (newVal) => {
    dialogType.value = newVal
  }
)

// 表单引用
const formRef = ref<FormInstance>()

// 年份值
const yearValue = ref(new Date().getFullYear())

// 供应商选项
const supplierOptions = ref<string[]>([])

/**
 * 计算金额
 * @param price 单价
 * @param quantity 数量
 * @returns 格式化的金额字符串
 */
const calculateMoney = (price: number, quantity: number): string => {
  return (price * quantity).toFixed(0)
}

// 表单数据
const form = reactive({
  code: '',
  year: '',
  stockInDate: new Date().getTime(),
  supplier: '',
  remark: '',
  details: [] as OemClothingStockInDetail[],
})

// 深度监听明细数据变化，自动更新金额
watch(
  () => form.details,
  (newDetails) => {
    // 遍历所有明细，更新金额
    newDetails.forEach((detail, index) => {
      // 只有当单价和数量都有值时才更新金额
      if (detail.price !== undefined && detail.quantity !== undefined) {
        form.details[index].money = calculateMoney(detail.price, detail.quantity)
      }
    })

    // 更新备注中的总数
    if (form.remark && form.remark.startsWith('总数：')) {
      const totalQuantity = newDetails.reduce((sum, detail) => sum + detail.quantity, 0)
      form.remark = `总数：${totalQuantity}`
    }
  },
  { deep: true }
)

// 表单校验规则
const formRules: FormRules = {
  stockInDate: [{ required: true, message: '请选择入库日期', trigger: 'change' }],
  year: [{ required: true, message: '请输入所属年份', trigger: 'blur' }],
  supplier: [{ required: true, message: '请选择供应商', trigger: 'change' }],
}

// 初始化
onMounted(() => {
  // 不在初始化时设置默认年份和加载供应商选项，而是在对话框打开时加载
})

/**
 * 加载最新年份
 */
const loadLatestYear = async (): Promise<void> => {
  try {
    // 获取年份选项
    const response = await getOemClothingIncomingYearOptions()
    console.log('获取到的年份选项:', response)

    if (response?.data?.years && response.data.years.length > 0) {
      // 年份选项已经按照倒序排列，第一个就是最新的
      const latestYear = response.data.years[0]
      console.log('最新年份:', latestYear)

      // 设置年份
      form.year = latestYear

      // 提取数字年份
      const yearMatch = latestYear.match(/\d+/)
      if (yearMatch) {
        yearValue.value = parseInt(yearMatch[0])
      }
    }
  } catch (error) {
    console.error('加载最新年份失败', error)
    // 如果失败，使用当前年份作为默认值
    yearValue.value = new Date().getFullYear()
    form.year = `${yearValue.value}年`
  }
}

/**
 * 根据年份加载供应商选项
 * @param year 年份
 */
const loadSuppliersByYear = async (year: string): Promise<void> => {
  try {
    console.log('正在加载供应商选项，年份:', year)
    const response = await getOemClothingIncomingSupplierOptions(year)
    console.log('获取到的供应商选项:', response)
    if (response?.data?.suppliers) {
      supplierOptions.value = response.data.suppliers
    } else {
      supplierOptions.value = []
    }
  } catch (error) {
    console.error('加载供应商选项失败', error)
    supplierOptions.value = []
  }
}

/**
 * 年份输入框值变化时更新表单年份和供应商选项
 */
const handleChangeYear = async (): Promise<void> => {
  // 将年份数字转换为带年字的格式
  form.year = `${yearValue.value}年`

  // 根据新的年份加载供应商选项
  await loadSuppliersByYear(form.year)

  // 清空当前选中的供应商
  form.supplier = ''
}

/**
 * 上传Excel文件变更
 * @param file 上传的文件
 */
const uploadChange = (file: any): void => {
  if (!file || !file.raw) {
    ElMessage.warning('请选择有效的Excel文件')
    return
  }

  const reader = new FileReader()
  reader.onload = (e: any) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      const worksheet = workbook.Sheets['OEM服装入库']
      const json = XLSX.utils.sheet_to_json(worksheet)

      // 过滤空行
      const filteredData = json.filter((row: any) => {
        return Object.values(row).some((val) => val !== null && val !== undefined && val !== '')
      })

      if (filteredData.length === 0) {
        ElMessage.warning('Excel文件中没有有效数据')
        return
      }

      // 转换数据格式
      const details: OemClothingStockInDetail[] = filteredData.map((item: any) => {
        // 尝试从不同的可能的字段名中获取值
        const oemClothingId = item.服装编码 || item.oem_clothing_id || item.oemClothingId || ''
        const oemClothingName =
          item.服装名称 || item.oem_clothing_name || item.oemClothingName || ''
        const style = item.款式 || item.style || ''
        const price = Number(item.单价 || item.price || 0)
        const quantity = Number(item.数量 || item.quantity || item.in_pcs || 0)

        // 计算金额
        const money = calculateMoney(price, quantity)

        return {
          oemClothingId,
          oemClothingName,
          style,
          price,
          quantity,
          money,
        }
      })

      // 更新表单数据
      form.details = details
      ElMessage.success(`成功导入 ${details.length} 条数据`)
    } catch (error) {
      console.error('解析Excel文件失败', error)
      ElMessage.error('解析Excel文件失败，请检查文件格式')
    }
  }
  reader.readAsArrayBuffer(file.raw)
}

/**
 * 获取OEM服装信息
 */
const getOemClothingInfo = async (): Promise<void> => {
  // 检查供应商是否已选择
  if (!form.supplier) {
    ElMessage.warning('请先选择供应商')
    return
  }

  // 检查年份是否已设置
  if (!form.year) {
    ElMessage.warning('请先设置所属年份')
    return
  }

  try {
    // 1. 根据所属年份和供应商查询服装信息
    const response = await getOemClothingList({
      page: 1,
      limit: 100,
      oem_clothing_year: form.year,
      oem_supplier: form.supplier,
    })
    console.log('获取到的OEM服装信息:', response)

    const oemClothingList = (response.data as any).oemClothingList

    // 如果明细表为空，则根据查询到的服装信息创建明细
    if (form.details.length === 0) {
      form.details = oemClothingList.map((item: any) => ({
        oemClothingId: item.oem_clothing_id,
        oemClothingName: item.oem_clothing_name,
        style: item.style || '',
        price: item.price || 0,
        quantity: 0, // 默认数量为0
        money: '0',
      }))
      ElMessage.success(`已添加 ${form.details.length} 条服装信息`)
    } else {
      // 如果明细表已有数据，则补充完善信息
      form.details = form.details.map((detail: OemClothingStockInDetail) => {
        // 尝试根据服装编码匹配
        let oemClothing = oemClothingList.find(
          (item: any) => item.oem_clothing_id === detail.oemClothingId
        )

        // 如果没有匹配到，尝试根据服装名称匹配
        if (!oemClothing && detail.oemClothingName) {
          oemClothing = oemClothingList.find(
            (item: any) => item.oem_clothing_name === detail.oemClothingName
          )
        }

        if (oemClothing) {
          const updatedPrice = detail.price || oemClothing.price || 0
          return {
            ...detail,
            oemClothingId: oemClothing.oem_clothing_id,
            oemClothingName: oemClothing.oem_clothing_name,
            style: oemClothing.style || detail.style || '',
            price: updatedPrice,
            money: calculateMoney(updatedPrice, detail.quantity),
          }
        }

        return detail
      })
      ElMessage.success('服装信息补齐完成')
    }

    // 2. 自动生成入库编码
    if (!form.code) {
      // 格式：OEMRK+时间戳
      form.code = 'OEMRK' + new Date().getTime()
    }

    // 3. 自动生成备注，格式为：总数：计算出来的总数
    const totalQuantity = form.details.reduce((sum, detail) => sum + detail.quantity, 0)
    form.remark = `总数：${totalQuantity}`
  } catch (error) {
    console.error('获取服装信息失败', error)
    ElMessage.error('获取服装信息失败，请检查网络连接')
  }
}

/**
 * 表格单元格双击编辑
 * @param row 行数据
 * @param column 列信息
 * @param cell 单元格DOM
 * @param event 事件对象
 */
const tableEdit = (row: any, column: any, cell: any, _event: any): void => {
  // 获取当前行索引
  const rowIndex = form.details.findIndex((item: OemClothingStockInDetail) => item === row)
  if (rowIndex === -1) return

  // 获取当前列属性名
  const prop = column.property
  if (!prop || prop === 'money' || prop === 'index') return

  // 创建输入框
  const input = document.createElement('input')
  input.value = row[prop] !== undefined ? row[prop].toString() : ''
  input.style.width = '100%'
  input.style.height = '100%'
  input.style.border = 'none'
  input.style.textAlign = 'center'

  // 清空单元格内容并添加输入框
  cell.innerHTML = ''
  cell.appendChild(input)
  input.focus()

  // 输入框失去焦点时保存数据
  input.onblur = () => {
    let value: any = input.value.trim()

    // 根据列属性处理数据类型
    if (prop === 'quantity' || prop === 'price') {
      value = parseFloat(value) || 0

      // 更新数据
      ;(form.details[rowIndex] as any)[prop] = value

      // 更新金额
      form.details[rowIndex].money = calculateMoney(
        form.details[rowIndex].price,
        form.details[rowIndex].quantity
      )
    } else {
      // 更新数据
      ;(form.details[rowIndex] as any)[prop] = value
    }

    // 恢复单元格显示
    cell.innerHTML = prop === 'money' ? form.details[rowIndex].money : value
  }

  // 按回车键保存
  input.onkeyup = (e) => {
    if (e.key === 'Enter') {
      input.blur()
    }
  }
}

/**
 * 计算表格合计行
 * @param param0 表格数据
 * @returns 合计行数据
 */
const getSummaries = ({
  columns,
  data,
}: {
  columns: any[]
  data: OemClothingStockInDetail[]
}): string[] => {
  const sums: string[] = []
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计'
      return
    }

    if (column.property === 'quantity') {
      const values = data.map((item) => Number(item.quantity))
      sums[index] = values.reduce((prev, curr) => prev + curr, 0).toString()
      return
    }

    if (column.property === 'money') {
      const values = data.map((item) => Number(item.money))
      sums[index] = values.reduce((prev, curr) => prev + curr, 0).toFixed(0)
      return
    }

    sums[index] = ''
  })

  return sums
}

/**
 * 关闭对话框时的处理
 */
const handleClose = (): void => {
  // 重置表单
  resetForm()
}

/**
 * 重置表单
 */
const resetForm = (): void => {
  // 重置表单验证
  if (formRef.value) {
    formRef.value.resetFields()
  }

  // 重置表单数据到初始状态
  form.code = ''
  form.year = ''
  form.stockInDate = new Date().getTime()
  form.supplier = ''
  form.remark = ''
  form.details = []

  // 不在重置时设置默认年份，而是在对话框打开时获取最新年份
}

/**
 * 提交表单
 */
const handleSubmit = async (): Promise<void> => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请完善表单信息')
      return
    }

    if (form.details.length === 0) {
      ElMessage.warning('请添加入库明细')
      return
    }

    // 提交表单
    emit('submit', {
      formData: {
        oem_clothing_incoming_id: form.code,
        oem_clothing_incoming_year: form.year,
        date_in: new Date(form.stockInDate),
        supplier: form.supplier,
        remark: form.remark,
      },
      details: form.details.map((detail: OemClothingStockInDetail) => ({
        oem_clothing_id: detail.oemClothingId,
        oem_clothing_name: detail.oemClothingName,
        style: detail.style,
        price: detail.price,
        in_pcs: detail.quantity,
        money: detail.money,
      })),
    })
  })
}

/**
 * 删除入库记录
 */
const handleDelete = (): void => {
  ElMessageBox.confirm('确定要删除该入库记录吗？', '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      emit('delete')
    })
    .catch(() => {
      // 取消删除
    })
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  setFormData: (data: any, details: any[] = []) => {
    form.code = data.oem_clothing_incoming_id || ''
    form.year = data.oem_clothing_incoming_year || ''
    form.stockInDate = data.date_in ? new Date(data.date_in).getTime() : new Date().getTime()
    form.supplier = data.supplier || ''
    form.remark = data.remark || ''

    // 设置年份值
    const yearMatch = form.year.match(/\d+/)
    if (yearMatch) {
      yearValue.value = parseInt(yearMatch[0])
    }

    // 设置明细
    form.details = details.map((detail) => {
      const price = detail.price || 0
      const quantity = detail.in_pcs || 0
      return {
        oemClothingId: detail.oem_clothing_id,
        oemClothingName: detail.oem_clothing_name,
        style: detail.style || '',
        price: price,
        quantity: quantity,
        money: detail.money || calculateMoney(price, quantity),
      }
    })
  },
})
</script>

<style scoped>
/* 新增的详情表格样式 */
.detail-table {
  margin-top: 20px;
  width: 100%;
  height: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid #e8e8e8;
}

/* 合计行样式 */
.detail-table :deep(.el-table__footer) {
  background-color: #f5f7fa;
}

.detail-table :deep(.el-table__footer-wrapper td) {
  background-color: #f5f7fa;
  color: #606266;
  font-size: 16px;
  text-align: center !important;
}

/* 确保合计行单元格内容居中 */
.detail-table :deep(.el-table__footer-wrapper .cell) {
  text-align: center !important;
}

.upload-demo {
  margin-right: 10px;
}

/* 响应式表单布局 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
}

.form-item {
  padding: 0 10px;
  box-sizing: border-box;
  margin-bottom: 20px;
  width: 33.33%;
}

.button-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 20px;
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .form-item {
    width: 50%;
  }
}

@media screen and (max-width: 768px) {
  .form-item {
    width: 100%;
  }

  .el-dialog {
    width: 95% !important;
  }
}
</style>
