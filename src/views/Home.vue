<template>
  <div class="app-container">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: isCollapsed }">
      <div class="sidebar-header">
        <div class="brand">
          <div class="logo-box">JY</div>
          
        </div>
      </div>

      <div class="menu-container">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          unique-opened
          router
          background-color="#2b3a4a"
          text-color="rgba(255, 255, 255, 0.85)"
          active-text-color="#ffffff"
          class="main-menu"
        >
          <el-menu-item index="/dashboard">
            <el-icon><Monitor /></el-icon>
            <template #title>工作台</template>
          </el-menu-item>

          <el-sub-menu index="/user">
            <template #title>
              <el-icon><UserFilled /></el-icon>
              <span>用户管理</span>
            </template>
            <el-menu-item index="/user/list">用户列表</el-menu-item>
            <el-menu-item index="/user/role">角色权限</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/fabrics">
            <template #title>
              <el-icon><Pear /></el-icon>
              <span>布料管理</span>
            </template>
            <el-menu-item index="/fabric/info">布料信息</el-menu-item>
            <el-menu-item index="/fabric/stock-in">布料入库</el-menu-item>
            <el-menu-item index="/fabric/group-info">布料组信息</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/staffs">
            <template #title>
              <el-icon><User /></el-icon>
              <span>员工管理</span>
            </template>
            <el-menu-item index="/staff/info">员工信息</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/clothings">
            <template #title>
              <el-icon><Goods /></el-icon>
              <span>服装管理</span>
            </template>
            <el-menu-item index="/clothing/info">服装信息</el-menu-item>
            <el-menu-item index="/oem-clothing/info">OEM服装信息</el-menu-item>
            <el-menu-item index="/oem-clothing/incoming">OEM服装入库</el-menu-item>
            <el-menu-item index="/transportation/info">发货信息</el-menu-item>
          </el-sub-menu>

          <el-sub-menu index="/works">
            <template #title>
              <el-icon><Document /></el-icon>
              <span>工序管理</span>
            </template>
            <el-menu-item index="/work/info">工序信息</el-menu-item>
            <el-menu-item index="/division-work/info">分工信息</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </div>

      <div class="sidebar-footer">
        <el-tooltip :content="isCollapsed ? '展开菜单' : '收起菜单'" placement="right" effect="light" popper-class="sidebar-tooltip">
          <el-button
            :icon="isCollapsed ? Expand : Fold"
            circle
            @click="toggleSidebar"
            class="collapse-btn"
          />
        </el-tooltip>
      </div>
    </aside>

    <!-- 主体区域 -->
    <div class="main-area">
      <!-- 头部导航 -->
      <header class="main-header">
        <div class="header-left">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item>{{ currentPage }}</el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <div class="header-right">
          <el-dropdown trigger="click" class="user-dropdown">
            <div class="user-info">
              <el-avatar :size="32" class="avatar" :src="userAvatar || ''">
                {{ userStore.userInfo?.userName?.charAt(0)?.toUpperCase() || 'U' }}
              </el-avatar>
              <span class="username">{{ userStore.userInfo?.userName || '用户' }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-icon><User /></el-icon>个人中心
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-icon><Lock /></el-icon>修改密码
                </el-dropdown-item>
                <el-dropdown-item divided @click="handleLogout">
                  <el-icon><SwitchButton /></el-icon>退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>

      <!-- 内容区域 -->

      <div class="content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter, useRoute } from 'vue-router'
import {
  Monitor,
  UserFilled,
  User,
  Fold,
  Expand,
  Lock,
  SwitchButton,
  ArrowDown,
  Document,
  Pear,
  Goods,
} from '@element-plus/icons-vue'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()

const isCollapsed = ref(true)
const userAvatar = ref('')

const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

// 活动菜单
const activeMenu = computed(() => {
  return route.path
})

// 当前页面标题
const currentPage = computed(() => {
  const path = route.path
  console.log('当前路径:', path) // 添加日志，帮助调试
  if (path.includes('/dashboard')) return '工作台'
  if (path.includes('/users')) return '用户管理'
  if (path.includes('/settings')) return '系统设置'
  if (path.includes('/fabric/info')) return '布料信息'
  if (path.includes('/fabric/stock-in')) return '布料入库'
  if (path.includes('/fabric/group-info')) return '布料组信息'
  if (path.includes('/staff/info')) return '员工信息'
  if (path.includes('/clothing/info')) return '服装信息'
  if (path.includes('/oem-clothing/info')) return 'OEM服装信息'
  if (path.includes('/oem-clothing/incoming')) return 'OEM服装入库'
  if (path.includes('/transportation/info')) return '发货信息'
  if (path.includes('/division-work/info')) return '分工信息'
  if (path.includes('/work/info')) return '工序信息'
  return '首页'
})

const handleLogout = () => {
  userStore.logout()
  router.push('/login')
}
</script>

<style scoped>
.app-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

/* ---------- 侧边栏样式 ---------- */
.sidebar {
  width: 200px; /* 从240px减小到200px */
  height: 100%;
  background: #2b3a4a; /* 简约的深蓝灰色，不使用渐变 */
  color: white;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  height: 64px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.brand {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.logo-box {
  min-width: 32px;
  height: 32px;
  border-radius: 6px;
  background: #409EFF; /* 使用简单的蓝色，与活动项边框颜色一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.brand h1 {
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
}

.menu-container {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 0;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
}

.collapse-btn {
  background: rgba(255, 255, 255, 0.08);
  border: none;
  color: white;
  transition: all 0.3s;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  transform: scale(1.05);
}

/* 覆盖Element菜单样式 */
:deep(.el-menu) {
  border-right: none;
}

:deep(.el-menu-item),
:deep(.el-sub-menu__title) {
  height: 50px;
  line-height: 50px;
}

:deep(.el-menu-item.is-active) {
  background-color: rgba(255, 255, 255, 0.18);
  color: white !important;
  border-left: 3px solid #409EFF; /* 添加左侧边框指示活动项 */
}

:deep(.el-menu-item:hover),
:deep(.el-sub-menu__title:hover) {
  background-color: rgba(255, 255, 255, 0.12);
}

:deep(.el-sub-menu.is-active .el-sub-menu__title) {
  color: white !important;
}

/* ---------- 主体区域样式 ---------- */
.main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f0f2f5;
  overflow: hidden;
}

/* 头部导航 */
.main-header {
  height: 64px;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.header-right {
  display: flex;
  align-items: center;
}

.action-item {
  padding: 0 12px;
  cursor: pointer;
}

.icon-btn {
  font-size: 20px;
  color: #606266;
}

.user-dropdown {
  margin-left: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f7fa;
}

.avatar {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.username {
  margin: 0 8px;
  font-size: 14px;
  color: #606266;
}

/* 内容区域 */
.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  overscroll-behavior: contain;
}

.dashboard-container {
  max-width: 1500px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
}

.welcome-section {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 24px;
}

.welcome-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.user-welcome h2 {
  font-size: 20px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.user-welcome p {
  color: #606266;
  margin: 0;
}

.stat-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.2;
}

.stat-title {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.content-placeholder {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 48px;
  display: flex;
  justify-content: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stat-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .welcome-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .welcome-header button {
    margin-top: 16px;
  }
}

/* 自定义滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 8px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #606266;
}

/* 自定义弹窗样式 */
:deep(.sidebar-tooltip) {
  background-color: white !important;
  color: #303133 !important;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.sidebar-tooltip .el-popper__arrow::before) {
  background-color: white !important;
  border-color: #e4e7ed !important;
}
</style>
