<template>
  <PageTemplate
    :queryParams="queryParams"
    :tableData="tableData"
    :total="total"
    :loading="loading"
    :tableHeight="650"
    @query="handleQuery"
    @reset="handleReset"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    @selection-change="handleSelectionChange"
  >
    <!-- 查询区域左侧 -->
    <template #query-form-left>
      <el-form-item label="所属年份" prop="years">
        <el-select
          v-model="queryParams.years"
          placeholder="选择年份"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleYearChange"
        >
          <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
        </el-select>
      </el-form-item>
      <el-form-item label="运输公司" prop="suppliers">
        <el-select
          v-model="queryParams.suppliers"
          placeholder="选择运输公司"
          clearable
          multiple
          collapse-tags
          :max-collapse-tags="1"
          style="width: 180px"
          @change="handleSupplierChange"
        >
          <el-option
            v-for="supplier in supplierOptions"
            :key="supplier"
            :label="supplier"
            :value="supplier"
          />
        </el-select>
      </el-form-item>
      <el-button type="info" @click="handleQueryNotArrived">未到货</el-button>
    </template>

    <!-- 查询区域右侧 -->
    <template #query-form-right>
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </template>

    <!-- 分页区域左侧 -->
    <template #pagination-left>
      <el-dropdown @command="handleExportCommand" split-button type="warning">
        Excel导出
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="all">导出全部数据</el-dropdown-item>
            <el-dropdown-item command="selected" :disabled="selectedRows.length === 0"
              >导出选中数据</el-dropdown-item
            >
            <el-dropdown-item command="current">导出当前页数据</el-dropdown-item>
            <el-dropdown-item command="details" :disabled="selectedRows.length === 0"
              >导出选中发货明细</el-dropdown-item
            >
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </template>

    <!-- 表格列 -->
    <template #table-columns>
      <el-table-column type="selection" width="40" align="center" />
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="transportation_id" label="发货编号" min-width="120" align="center" />
      <el-table-column prop="transportation_year" label="所属年份" width="100" align="center" />
      <el-table-column prop="date_out" label="发货日期" width="120" align="center">
        <template #default="scope">
          {{ formatDate(scope.row.date_out) }}
        </template>
      </el-table-column>
      <el-table-column label="天数" width="60" align="center">
        <template #default="scope">
          {{ calculateDays(scope.row.date_out, scope.row.date_arrived) }}
        </template>
      </el-table-column>
      <el-table-column prop="date_arrived" label="到货日期" width="120" align="center">
        <template #default="scope">
          {{ scope.row.date_arrived ? formatDate(scope.row.date_arrived) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="supplier" label="运输公司" min-width="120" align="center" />
      <el-table-column prop="total_package_quantity" label="包裹数量" width="100" align="center" />
      <el-table-column prop="total_pcs" label="总件数" width="100" align="center" />
      <el-table-column prop="weight" label="重量" width="100" align="center" />
      <el-table-column prop="price" label="价格" width="100" align="center" />

      <el-table-column
        prop="remark"
        show-overflow-tooltip
        label="备注"
        min-width="150"
        align="center"
      />
      <el-table-column label="操作" width="180" fixed="right" align="center">
        <template #default="scope">
          <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </template>

    <!-- 额外内容插槽 -->
    <template #extra-content>
      <!-- 发货信息表单对话框 -->
      <TransportationDialog
        v-model:visible="dialogVisible"
        :type="dialogType"
        :current-id="currentId"
        ref="dialogRef"
        @submit="handleSubmit"
        @delete="handleDelete"
      />
    </template>
  </PageTemplate>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as XLSX from 'xlsx'
// 导入通用页面模板
import PageTemplate from '@/components/common/PageTemplate.vue'
// 导入对话框组件
import TransportationDialog from './components/TransportationDialog.vue'
import {
  getTransportationList,
  createTransportation,
  updateTransportation,
  updateTransportationWithDetails,
  deleteTransportation,
  getTransportationYearOptions,
  getTransportationSupplierOptions,
  createTransportationDetailBatch,
  getTransportationDetailsExportData,
} from '@/api/transportation'
import { formatDate } from '@/utils/date'

// 计算天数差异
function calculateDays(dateOut: string | Date, dateArrived?: string | Date): number {
  if (!dateOut) return 0

  const startDate = new Date(dateOut)
  const endDate = dateArrived ? new Date(dateArrived) : new Date() // 如果没有到货日期，使用当前日期

  // 计算天数差异（毫秒转天数）
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  return diffDays
}

// 定义本地类型
interface Transportation {
  _id: string
  transportation_id: string
  transportation_year: string
  date_out: string | Date
  date_arrived?: string | Date
  supplier: string
  total_package_quantity?: number
  total_pcs?: number
  weight?: number
  price?: number
  shipping_method?: string
  remark?: string
  transportation_img?: Array<{ url: string; Key: string }>
  createTime?: string | Date
}

interface QueryParams {
  years: string[]
  suppliers: string[]
  page: number
  limit: number
  notArrived?: boolean
}

interface SubmitData {
  transportation_id: string
  transportation_year: string
  date_out: Date
  date_arrived?: Date
  supplier: string
  total_package_quantity?: number
  total_pcs?: number
  weight?: number
  price?: number
  shipping_method?: string
  remark?: string
  transportation_img?: Array<{ url: string; Key: string }>
}

interface SubmitDetailData {
  series_number?: number
  clothing_name: string
  package_quantity?: number
  QUP?: number
  out_pcs: number
  oem?: string
  clothing_id: string
  style?: string
}

// 加载状态
const loading = ref(false)

// 表格数据
const tableData = ref<Transportation[]>([])
const total = ref(0)

// 查询参数
const queryParams = reactive<QueryParams>({
  years: [],
  suppliers: [],
  page: 1,
  limit: 10,
  notArrived: false,
})

// 年份和供应商选项
const yearOptions = ref<string[]>([])
const supplierOptions = ref<string[]>([])

// 对话框控制
const dialogVisible = ref(false)
const dialogType = ref<'add' | 'edit'>('add')
const currentId = ref('')
const dialogRef = ref()

// 选中行数据
const selectedRows = ref<Transportation[]>([])

// 处理表格选择变化
function handleSelectionChange(selection: Transportation[]) {
  selectedRows.value = selection
}

// 生命周期钩子
onMounted(async () => {
  await loadOptions()
  handleQuery()
})

// 加载选项数据
async function loadOptions() {
  try {
    // 加载年份选项
    const yearResponse = await getTransportationYearOptions()
    yearOptions.value = yearResponse.data.years || []

    // 加载供应商选项
    const supplierResponse = await getTransportationSupplierOptions()
    supplierOptions.value = supplierResponse.data.suppliers || []
  } catch (error) {
    console.error('加载选项数据失败:', error)
    ElMessage.error('加载选项数据失败')
  }
}

// 年份变化处理
async function handleYearChange() {
  try {
    // 根据选择的年份更新供应商选项
    const years = queryParams.years.join(',')
    const response = await getTransportationSupplierOptions(years)
    supplierOptions.value = response.data.suppliers || []

    // 自动执行查询
    handleQuery()
  } catch (error) {
    console.error('加载供应商选项失败:', error)
  }
}

// 供应商变化处理
async function handleSupplierChange() {
  try {
    // 根据选择的供应商更新年份选项
    const suppliers = queryParams.suppliers.join(',')
    const response = await getTransportationYearOptions(suppliers)
    yearOptions.value = response.data.years || []

    // 自动执行查询
    handleQuery()
  } catch (error) {
    console.error('加载年份选项失败:', error)
  }
}

// 查询未到货数据
async function handleQueryNotArrived() {
  // 重置分页
  queryParams.page = 1
  // 设置未到货标志
  queryParams.notArrived = true
  queryParams.limit = 1000
  // 执行查询
  handleQuery()
  queryParams.notArrived = false
  queryParams.limit = 10
}

// 查询数据
async function handleQuery() {
  loading.value = true
  try {
    // 构建查询参数
    const params: any = {
      transportation_years: queryParams.years,
      suppliers: queryParams.suppliers,
      page: queryParams.page,
      limit: queryParams.limit,
    }

    // 如果是查询未到货数据，添加特殊处理
    if (queryParams.notArrived) {
      // 在前端处理未到货筛选
      const allResponse = await getTransportationList(params)

      // 处理API响应
      let allData: Transportation[] = []
      if (allResponse) {
        if (
          typeof allResponse === 'object' &&
          'data' in allResponse &&
          Array.isArray(allResponse.data)
        ) {
          allData = allResponse.data
        } else if (Array.isArray(allResponse)) {
          allData = allResponse
        }
      }

      // 筛选未到货的数据
      const notArrivedData = allData.filter((item) => !item.date_arrived)
      tableData.value = notArrivedData
      total.value = notArrivedData.length

      loading.value = false
      return
    }

    // 正常查询
    const response = await getTransportationList(params)

    // 处理API响应
    if (response) {
      // 根据后端API的实际响应结构进行处理
      if (typeof response === 'object' && 'data' in response && 'total' in response) {
        // 如果响应格式是 { data: [...], total: number }
        tableData.value = response.data || []
        total.value = typeof response.total === 'number' ? response.total : 0
      } else if (Array.isArray(response)) {
        // 如果响应直接是数组
        tableData.value = response
        total.value = response.length
      } else if (typeof response === 'object' && Array.isArray(response.data)) {
        // 如果响应格式是 { data: [...] }，但没有total字段
        tableData.value = response.data
        total.value = response.data.length
      } else {
        // 其他情况，设置为空数组
        tableData.value = []
        total.value = 0
        console.warn('未知的API响应格式:', response)
      }
    } else {
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('查询发货信息失败:', error)
    ElMessage.error('查询发货信息失败')
    tableData.value = []
    total.value = 0
  } finally {
    loading.value = false
  }
}

// 重置查询
function handleReset() {
  queryParams.years = []
  queryParams.suppliers = []
  queryParams.page = 1
  queryParams.limit = 10
  queryParams.notArrived = false // 重置未到货标志
  handleQuery()
  loadOptions()
}

// 分页大小变化
function handleSizeChange(val: number) {
  queryParams.limit = val
  handleQuery()
}

// 当前页变化
function handleCurrentChange(val: number) {
  queryParams.page = val
  handleQuery()
}

// 新增发货信息
function handleAdd() {
  dialogType.value = 'add'
  currentId.value = ''
  dialogVisible.value = true
}

// 编辑发货信息
function handleEdit(row: Transportation) {
  dialogType.value = 'edit'
  // 处理可能的API响应格式差异
  currentId.value = typeof row._id === 'string' ? row._id : (row as any).id || row._id
  dialogVisible.value = true
}

// 删除发货信息
function handleDelete(row: Transportation) {
  ElMessageBox.confirm(`确定要删除发货信息 ${row.transportation_id} 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        // 处理可能的API响应格式差异
        const id = typeof row._id === 'string' ? row._id : (row as any).id || row._id
        await deleteTransportation(id)
        ElMessage.success('删除成功')
        handleQuery()
        loadOptions()
      } catch (error) {
        console.error('删除发货信息失败:', error)
        ElMessage.error('删除发货信息失败')
      }
    })
    .catch(() => {
      // 取消删除
    })
}

// 提交表单
async function handleSubmit(data: { formData: SubmitData; details: SubmitDetailData[] }) {
  try {
    if (dialogType.value === 'add') {
      // 创建发货信息
      const response = await createTransportation(data.formData)

      // 根据API响应格式获取ID
      let transportationId = ''
      if (response && typeof response === 'object') {
        if ('_id' in response) {
          transportationId = response._id as string
        } else if (
          'data' in response &&
          typeof response.data === 'object' &&
          response.data &&
          '_id' in response.data
        ) {
          transportationId = (response.data as any)._id
        } else if ('id' in response) {
          transportationId = response.id as string
        }
      }

      if (!transportationId) {
        console.error('无法获取创建的发货信息ID:', response)
        ElMessage.error('创建发货信息成功，但无法获取ID，请刷新页面')
        dialogVisible.value = false
        handleQuery()
        loadOptions()
        return
      }

      // 创建发货明细
      if (data.details && data.details.length > 0) {
        await createTransportationDetailBatch(transportationId, data.details)
      }

      ElMessage.success('创建成功')
    } else {
      // 更新发货信息及其明细
      if (data.details && data.details.length > 0) {
        // 使用新的API一次性更新发货信息和明细
        await updateTransportationWithDetails(currentId.value, data.formData, data.details)
      } else {
        // 如果没有明细数据，只更新发货信息
        await updateTransportation(currentId.value, data.formData)
      }

      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    handleQuery()
    loadOptions()
  } catch (error) {
    console.error('保存发货信息失败:', error)
    ElMessage.error('保存发货信息失败')
  }
}

// 处理导出命令
function handleExportCommand(command: string) {
  switch (command) {
    case 'all':
      handleExportAll()
      break
    case 'selected':
      handleExportSelected()
      break
    case 'current':
      handleExportCurrent()
      break
    case 'details':
      handleExportDetails()
      break
  }
}

// 导出全部数据
async function handleExportAll() {
  try {
    // 检查是否已选择所属年份
    if (!queryParams.years || queryParams.years.length === 0) {
      ElMessage.warning('请先选择所属年份后再导出全部数据')
      return
    }

    // 显示加载中
    loading.value = true
    ElMessage.info('正在准备导出所有数据，请稍候...')

    // 构建查询参数，获取所有数据
    const exportParams: any = {
      transportation_years: queryParams.years,
      suppliers: queryParams.suppliers,
      page: 1,
      limit: 5000, // 设置较大的限制数，确保获取到所有数据
    }

    // 如果是未到货筛选，添加相应参数
    if (queryParams.notArrived) {
      exportParams.notArrived = true
    }

    // 获取所有符合条件的数据
    const response = await getTransportationList(exportParams)

    // 处理API响应
    let exportData = []
    if (response) {
      if (typeof response === 'object' && 'data' in response) {
        exportData = response.data || []
      } else if (Array.isArray(response)) {
        exportData = response
      }
    }

    if (exportData.length === 0) {
      ElMessage.warning('没有数据可导出')
      loading.value = false
      return
    }

    // 导出数据
    exportToExcel(exportData, '全部')
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出Excel失败')
  } finally {
    loading.value = false
  }
}

// 导出选中数据
function handleExportSelected() {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出的数据')
    return
  }

  exportToExcel(selectedRows.value, '选中')
}

// 导出当前页数据
function handleExportCurrent() {
  if (tableData.value.length === 0) {
    ElMessage.warning('当前页没有数据可导出')
    return
  }

  exportToExcel(tableData.value, '当前页')
}

// 导出发货明细数据
async function handleExportDetails() {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请先选择要导出明细的发货信息')
    return
  }

  try {
    loading.value = true
    ElMessage.info('正在准备导出发货明细数据，请稍候...')

    // 1. 获取选中的发货ID列表
    const transportationIds = selectedRows.value.map((transportation) =>
      typeof transportation._id === 'string'
        ? transportation._id
        : (transportation as any).id || transportation._id
    )

    // 2. 调用新的API获取导出数据
    const response = await getTransportationDetailsExportData(transportationIds)
    console.log('获取到的发货明细导出数据：', response)

    // 3. 处理API响应
    let exportData: { details: any[]; clothingInfo: any[] } = { details: [], clothingInfo: [] }

    if (response) {
      // 处理可能的不同响应格式
      if (typeof response === 'object') {
        if ('data' in response) {
          // 如果响应格式是 { data: { details, clothingInfo } }
          const responseData = response.data
          if (responseData && typeof responseData === 'object') {
            if ('details' in responseData && 'clothingInfo' in responseData) {
              exportData = responseData as { details: any[]; clothingInfo: any[] }
            }
          }
        } else if ('details' in response && 'clothingInfo' in response) {
          // 如果响应格式直接是 { details, clothingInfo }
          exportData = response as { details: any[]; clothingInfo: any[] }
        }
      }
    }

    if (
      !exportData.details ||
      !exportData.clothingInfo ||
      !Array.isArray(exportData.details) ||
      !Array.isArray(exportData.clothingInfo)
    ) {
      ElMessage.warning('获取导出数据失败，返回数据格式不正确')
      loading.value = false
      return
    }

    // 4. 分离普通服装和OEM服装数据
    const normalClothingData: any[] = []
    const oemClothingDataArray: any[] = []

    // 处理服装信息
    for (const clothing of exportData.clothingInfo) {
      if (clothing.oem === '是') {
        // OEM服装
        oemClothingDataArray.push({
          款号: clothing.clothing_name || '',
          入库数量: clothing.in_pcs || 0,
          出货数量: clothing.shipments || 0,
          服装编码: clothing.clothing_id || '',
        })
      } else {
        // 普通服装
        normalClothingData.push({
          款号: clothing.clothing_name || '',
          裁剪数量: clothing.clipping_pcs || 0,
          出货数量: clothing.shipments || 0,
          服装编码: clothing.clothing_id || '',
        })
      }
    }

    // 5. 创建工作簿
    const wb = XLSX.utils.book_new()

    // 添加普通服装工作表
    if (normalClothingData.length > 0) {
      const normalWs = XLSX.utils.json_to_sheet(normalClothingData)

      // 设置列宽
      const normalColWidths = [
        { wch: 20 }, // 款号
        { wch: 10 }, // 裁剪数量
        { wch: 10 }, // 出货数量
        { wch: 15 }, // 服装编码
      ]
      normalWs['!cols'] = normalColWidths

      XLSX.utils.book_append_sheet(wb, normalWs, '普通服装明细')
    }

    // 添加OEM服装工作表
    if (oemClothingDataArray.length > 0) {
      const oemWs = XLSX.utils.json_to_sheet(oemClothingDataArray)

      // 设置列宽
      const oemColWidths = [
        { wch: 20 }, // 款号
        { wch: 10 }, // 入库数量
        { wch: 10 }, // 出货数量
        { wch: 15 }, // 服装编码
      ]
      oemWs['!cols'] = oemColWidths

      XLSX.utils.book_append_sheet(wb, oemWs, 'OEM服装明细')
    }

    // 生成文件名
    const fileName = `发货明细_${new Date().toISOString().split('T')[0]}`

    // 导出文件
    XLSX.writeFile(wb, `${fileName}.xlsx`)
    ElMessage.success(
      `导出成功，普通服装 ${normalClothingData.length} 条，OEM服装 ${oemClothingDataArray.length} 条，明细 ${exportData.details.length} 条`
    )
  } catch (error) {
    console.error('导出发货明细失败', error)
    ElMessage.error('导出发货明细失败')
  } finally {
    loading.value = false
  }
}

// 通用导出函数
function exportToExcel(data: Transportation[], exportType: string) {
  try {
    // 准备导出数据
    const excelData = data.map((item) => ({
      发货编号: item.transportation_id,
      所属年份: item.transportation_year,
      发货日期: formatDate(item.date_out),
      天数: calculateDays(item.date_out, item.date_arrived),
      到货日期: item.date_arrived ? formatDate(item.date_arrived) : '',
      运输公司: item.supplier,
      包裹数量: item.total_package_quantity || '',
      总件数: item.total_pcs || '',
      重量: item.weight || '',
      价格: item.price || '',
      运输方式: item.shipping_method || '',
      备注: item.remark || '',
    }))

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(excelData)

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 发货编号
      { wch: 10 }, // 所属年份
      { wch: 12 }, // 发货日期
      { wch: 8 }, // 天数
      { wch: 12 }, // 到货日期
      { wch: 15 }, // 运输公司
      { wch: 10 }, // 包裹数量
      { wch: 10 }, // 总件数
      { wch: 10 }, // 重量
      { wch: 10 }, // 价格
      { wch: 12 }, // 运输方式
      { wch: 30 }, // 备注
    ]
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '发货信息')

    // 生成文件名
    let fileName = `发货信息_${exportType}_${new Date().toISOString().split('T')[0]}`

    // 添加筛选条件到文件名
    if (queryParams.years && queryParams.years.length > 0) {
      fileName += `_年份${queryParams.years.join('-')}`
    }
    if (queryParams.suppliers && queryParams.suppliers.length > 0) {
      fileName += `_运输公司${queryParams.suppliers.join('-')}`
    }
    if (queryParams.notArrived) {
      fileName += '_未到货'
    }

    // 导出文件
    XLSX.writeFile(wb, `${fileName}.xlsx`)
    ElMessage.success(`导出成功，共 ${excelData.length} 条数据`)
  } catch (error) {
    console.error('导出失败', error)
    ElMessage.error('导出Excel失败')
  }
}
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
