<template>
  <el-dialog
    :title="type === 'add' ? '新增发货信息' : '编辑发货信息'"
    v-model="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="transportation-dialog"
  >
    <div
      v-loading="loading"
      element-loading-text="数据加载中..."
      element-loading-background="rgba(255, 255, 255, 0.8)"
      class="dialog-content-wrapper"
    >
      <el-tabs v-model="activeTab" class="transportation-tabs">
        <!-- 基本信息选项卡 -->
        <el-tab-pane label="基本信息" name="basic">
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="100px"
            class="transportation-form"
          >
            <div class="form-row">
              <el-form-item label="所属年份" prop="year">
                <el-select
                  v-model="form.year"
                  placeholder="选择年份"
                  filterable
                  allow-create
                  @change="handleYearChange"
                  style="width: 100%"
                >
                  <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
                </el-select>
              </el-form-item>
              <el-form-item label="运输公司" prop="supplier">
                <el-select
                  v-model="form.supplier"
                  placeholder="运输公司"
                  filterable
                  allow-create
                  style="width: 100%"
                >
                  <el-option
                    v-for="supplier in supplierOptions"
                    :key="supplier"
                    :label="supplier"
                    :value="supplier"
                  />
                </el-select>
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="发货日期" prop="shipmentDate">
                <el-date-picker
                  v-model="form.shipmentDate"
                  type="date"
                  placeholder="选择发货日期"
                  style="width: 100%"
                />
              </el-form-item>
              <el-form-item label="到货日期" prop="arrivalDate">
                <el-date-picker
                  v-model="form.arrivalDate"
                  type="date"
                  placeholder="选择到货日期"
                  style="width: 100%"
                />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="发货编码" prop="code">
                <el-input :disabled="props.type === 'edit'" v-model="form.code" />
              </el-form-item>
              <el-form-item label="运输方式" prop="shippingMethod">
                <el-input v-model="form.shippingMethod" placeholder="运输方式" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="包裹数量" prop="totalPackageQuantity">
                <el-input v-model.number="form.totalPackageQuantity" placeholder="包裹数量" />
              </el-form-item>
              <el-form-item label="总件数" prop="totalPcs">
                <el-input v-model.number="form.totalPcs" placeholder="总件数" />
              </el-form-item>
            </div>
            <div class="form-row">
              <el-form-item label="重量" prop="weight">
                <el-input v-model.number="form.weight" placeholder="重量" />
              </el-form-item>
              <el-form-item label="价格" prop="price">
                <el-input v-model.number="form.price" placeholder="价格" />
              </el-form-item>
            </div>

            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入备注" />
            </el-form-item>

            <el-form-item label="图片" prop="transportationImg">
              <div class="image-container">
                <!-- 左侧：图片预览组件 -->
                <div class="image-preview-section">
                  <div v-if="fileList.length > 0" class="image-preview-container">
                    <el-image
                      v-for="(file, index) in fileList"
                      :key="index"
                      :src="file.url"
                      fit="contain"
                      :preview-src-list="getPreviewSrcList()"
                      :initial-index="index"
                      class="preview-image-item"
                      :z-index="3000"
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                          <div class="error-text">加载失败</div>
                        </div>
                      </template>
                    </el-image>
                  </div>
                </div>

                <!-- 右侧：上传组件 - 只显示文件名 -->
                <div class="upload-section">
                  <el-upload
                    class="file-upload"
                    action="#"
                    :auto-upload="false"
                    :file-list="fileList"
                    :on-change="handleImageChange"
                    :on-remove="handleFileRemove"
                    :show-file-list="true"
                    list-type="text"
                  >
                    <el-button type="primary">
                      <el-icon><Plus /></el-icon>
                      选择图片
                    </el-button>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 明细选项卡 -->
        <el-tab-pane label="明细" name="details">
          <div class="button-row">
            <el-upload
              class="upload-demo"
              action=""
              :show-file-list="false"
              :auto-upload="false"
              :on-change="uploadChange"
            >
              <el-button type="primary">点击上传</el-button>
            </el-upload>
            <el-button type="primary" @click="getClothingInfo">补齐信息</el-button>
            <div class="clothing-year-input">
              <el-input-number
                v-model="clothingYear"
                :min="2000"
                :max="2100"
                :precision="0"
                :controls="true"
                placeholder="服装年份"
                style="width: 150px"
              />
            </div>
          </div>
          <el-table
            class="detail-table"
            :data="form.details"
            border
            style="width: 100%"
            height="400"
          >
            <el-table-column prop="seriesNumber" label="包序" width="60" align="center" />
            <el-table-column prop="clothingId" label="服装编号" min-width="120" align="center" />
            <el-table-column prop="clothingName" label="服装名称" min-width="120" align="center" />
            <el-table-column prop="style" label="款式" min-width="120" align="center" />
            <el-table-column prop="packageQuantity" label="包裹数量" width="100" align="center">
              <template #default="scope">
                <el-input v-model.number="scope.row.packageQuantity" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="QUP" label="每包数量" width="100" align="center">
              <template #default="scope">
                <el-input v-model.number="scope.row.QUP" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="outPcs" label="出货数量" width="100" align="center">
              <template #default="scope">
                <el-input v-model.number="scope.row.outPcs" size="small" />
              </template>
            </el-table-column>
            <el-table-column prop="oem" label="是否OEM" width="100" align="center">
              <template #default="scope">
                <el-select v-model="scope.row.oem" placeholder="是否OEM" size="small">
                  <el-option label="是" value="是" />
                  <el-option label="否" value="否" />
                </el-select>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules, UploadFile, UploadUserFile } from 'element-plus'
import * as XLSX from 'xlsx'
import { Picture, Plus } from '@element-plus/icons-vue'
import {
  getTransportationSupplierOptions,
  getTransportationYearOptions,
  getTransportationDetailsByTransportationId,
  getLatestTransportationId,
  getTransportationById,
} from '@/api/transportation'
import { findClothingByNames } from '@/api/clothing'
import { findOemClothingByNames } from '@/api/oemClothing'
import { uploadImage, deleteImages, UploadType } from '@/utils/upload'

// 定义本地类型
interface TransportationShipmentDetail {
  seriesNumber?: number
  clothingId: string
  clothingName: string
  style?: string
  packageQuantity?: number
  QUP?: number
  outPcs: number
  oem?: string
}

interface TransportationImage {
  url: string
  Key: string
}

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  type: {
    type: String as () => 'add' | 'edit',
    required: true,
  },
  currentId: {
    type: String,
    default: '',
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'delete'])

// 对话框可见性
const dialogVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 表单引用
const formRef = ref<FormInstance>()

// 不需要这个变量，已移除

// 供应商选项
const supplierOptions = ref<string[]>([])
const yearOptions = ref<string[]>([])

// 文件列表
const fileList = ref<any[]>([])

// 待上传的文件列表
const pendingUploadFiles = ref<File[]>([])

// 需要从服务器删除的图片
const imagesToDelete = ref<{ Key: string }[]>([])

// 当前激活的选项卡
const activeTab = ref('basic')

// 服装年份
const clothingYear = ref(new Date().getFullYear())

// 加载状态
const loading = ref(false)

// 表单数据
const form = reactive({
  code: '',
  year: '',
  shipmentDate: new Date().getTime(),
  arrivalDate: undefined as number | undefined,
  supplier: '',
  totalPackageQuantity: undefined as number | undefined,
  totalPcs: undefined as number | undefined,
  weight: undefined as number | undefined,
  price: undefined as number | undefined,
  shippingMethod: '',
  remark: '',
  details: [] as TransportationShipmentDetail[],
  transportationImg: [] as TransportationImage[],
})

// 表单验证规则
const rules = reactive<FormRules>({
  year: [{ required: true, message: '请选择所属年份', trigger: 'change' }],
  shipmentDate: [{ required: true, message: '请选择发货日期', trigger: 'change' }],
  supplier: [{ required: true, message: '请选择运输公司', trigger: 'change' }],
})

// 监听当前ID变化，加载数据
watch(
  () => props.currentId,
  async (newVal) => {
    if (newVal && props.type === 'edit') {
      // await loadTransportationData(newVal)
    }
  },
  { immediate: true }
)

// 监听对话框可见性
watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      await loadOptions()
      if (props.type === 'add') {
        resetForm()
      } else if (props.type === 'edit' && props.currentId) {
        // 确保在编辑模式下打开弹窗时加载数据
        await loadTransportationData(props.currentId)
      }
    }
  }
)

// 生命周期钩子
onMounted(async () => {
  await loadOptions()
})

// 加载选项数据
async function loadOptions() {
  try {
    loading.value = true

    // 加载年份选项
    const yearResponse = await getTransportationYearOptions()
    yearOptions.value = yearResponse.data.years || []

    // 如果是新增，默认选择最新年份
    if (props.type === 'add' && yearOptions.value.length > 0) {
      form.year = yearOptions.value[0]
    }

    // 加载供应商选项，根据当前选择的年份
    const selectedYear = form.year || (yearOptions.value.length > 0 ? yearOptions.value[0] : '')
    if (selectedYear) {
      const supplierResponse = await getTransportationSupplierOptions(selectedYear)
      supplierOptions.value = supplierResponse.data.suppliers || []
    } else {
      // 如果没有选择年份，加载所有供应商
      const supplierResponse = await getTransportationSupplierOptions()
      supplierOptions.value = supplierResponse.data.suppliers || []
    }
  } catch (error) {
    console.error('加载选项数据失败:', error)
    ElMessage.error('加载选项数据失败')
  } finally {
    loading.value = false
  }
}

// 加载发货信息数据
async function loadTransportationData(id: string) {
  try {
    // 设置加载状态
    loading.value = true

    // 显示加载中提示
    ElMessage.info('正在加载数据，请稍候...')

    // 加载发货信息主表数据
    const response = await getTransportationById(id)
    console.log('获取到的发货信息数据:', response)

    // 正确处理响应数据，兼容不同的API响应格式
    let transportationData: any = null
    if (response && typeof response === 'object') {
      // 直接使用response或response.data
      transportationData = response.data ? response.data : response
    }

    if (!transportationData) {
      ElMessage.error('未获取到有效的发货信息数据')
      return
    }

    console.log('处理后的发货信息数据:', transportationData)

    // 填充表单数据
    form.code = transportationData.transportation_id || ''
    form.year = transportationData.transportation_year || ''
    form.shipmentDate = transportationData.date_out
      ? new Date(transportationData.date_out).getTime()
      : new Date().getTime()
    form.arrivalDate = transportationData.date_arrived
      ? new Date(transportationData.date_arrived).getTime()
      : undefined
    form.supplier = transportationData.supplier || ''
    form.totalPackageQuantity = transportationData.total_package_quantity
    form.totalPcs = transportationData.total_pcs
    form.weight = transportationData.weight
    form.price = transportationData.price
    form.shippingMethod = transportationData.shipping_method || ''
    form.remark = transportationData.remark || ''
    form.transportationImg = transportationData.transportation_img || []

    // 设置文件列表
    fileList.value = (transportationData.transportation_img || []).map(
      (img: any, index: number) => {
        // 先创建一个基本对象，然后转换为UploadUserFile
        const fileObj = {
          name: img.Key.split('/').pop() || `图片${index + 1}`,
          url: img.url,
          uid: index, // 使用数字类型的uid
          customData: img, // 使用自定义属性存储原始数据
        }
        return fileObj as unknown as UploadUserFile
      }
    )

    // 加载发货明细数据
    const detailsResponse = await getTransportationDetailsByTransportationId(transportationData.transportation_id || '')
    console.log('获取到的发货明细数据:', detailsResponse)

    // 正确处理明细响应数据
    let detailsData: any[] = []
    if (detailsResponse) {
      if (Array.isArray(detailsResponse)) {
        detailsData = detailsResponse
      } else if (detailsResponse.data && Array.isArray(detailsResponse.data)) {
        detailsData = detailsResponse.data
      }
    }

    if (detailsData.length > 0) {
      // 转换明细数据格式
      form.details = detailsData.map((detail) => ({
        seriesNumber: detail.series_number,
        clothingId: detail.clothing_id,
        clothingName: detail.clothing_name,
        style: detail.style,
        packageQuantity: detail.package_quantity,
        QUP: detail.QUP,
        outPcs: detail.out_pcs,
        oem: detail.oem,
      }))
    } else {
      form.details = []
    }

    // 数据加载完成提示
    ElMessage.success('数据加载完成')
  } catch (error) {
    console.error('加载发货信息失败:', error)
    ElMessage.error('加载发货信息失败，请检查网络连接或刷新页面重试')
  } finally {
    // 重置加载状态
    loading.value = false
  }
}

// 重置表单
function resetForm() {
  form.code = ''
  form.year = yearOptions.value.length > 0 ? yearOptions.value[0] : ''
  form.shipmentDate = new Date().getTime()
  form.arrivalDate = undefined
  form.supplier = ''
  form.totalPackageQuantity = undefined
  form.totalPcs = undefined
  form.weight = undefined
  form.price = undefined
  form.shippingMethod = ''
  form.remark = ''
  form.details = []
  form.transportationImg = []
  fileList.value = []

  // 清空待上传文件列表
  pendingUploadFiles.value = []

  // 清空待删除图片列表
  imagesToDelete.value = []
}

// 处理图片变化
function handleImageChange(file: UploadFile) {
  console.log('图片变化:', file)

  if (file.raw) {
    // 将文件添加到待上传列表
    pendingUploadFiles.value.push(file.raw)

    // 创建临时预览
    const reader = new FileReader()
    reader.onload = (e) => {
      const newFile = {
        name: file.name,
        url: e.target?.result as string,
        uid: file.uid,
        status: 'ready',
        raw: file.raw,
      }

      // 更新文件列表
      const index = fileList.value.findIndex((f) => f.uid === file.uid)
      if (index !== -1) {
        fileList.value[index] = newFile
      } else {
        fileList.value.push(newFile)
      }
    }
    reader.readAsDataURL(file.raw)
  }
}

// 文件移除
function handleFileRemove(uploadFile: UploadFile) {
  console.log('文件移除:', uploadFile)

  // 如果是已有的图片（有customData），标记为需要删除
  if ((uploadFile as any).customData) {
    // 将图片添加到待删除列表，在提交时处理
    const customData = (uploadFile as any).customData
    imagesToDelete.value.push({ Key: customData.Key })
    console.log('标记图片需要删除:', customData.Key)
  } else if (uploadFile.raw) {
    // 如果是待上传的图片，从待上传列表中移除
    const index = pendingUploadFiles.value.findIndex((file) => file === uploadFile.raw)
    if (index !== -1) {
      pendingUploadFiles.value.splice(index, 1)
    }
  }

  // 从文件列表中移除
  const index = fileList.value.findIndex((file) => file.uid === uploadFile.uid)
  if (index !== -1) {
    fileList.value.splice(index, 1)
  }
}

// 获取所有图片的URL列表，用于预览
function getPreviewSrcList() {
  return fileList.value.map((file) => file.url || '').filter((url) => url)
}

// 处理Excel上传
function uploadChange(file: UploadFile) {
  if (!file.raw) {
    ElMessage.error('文件上传失败')
    return
  }

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = e.target?.result
      const workbook = XLSX.read(data, { type: 'array' })
      const worksheet = workbook.Sheets['发货明细']
      const json = XLSX.utils.sheet_to_json(worksheet)

      // 过滤空行
      const filteredData = json.filter((row: any) => {
        return Object.values(row).some((val) => val !== null && val !== undefined && val !== '')
      })
      console.log('filteredData', filteredData)
      if (filteredData.length === 0) {
        ElMessage.warning('Excel文件中没有有效数据')
        return
      }

      // 解析Excel数据
      const details: TransportationShipmentDetail[] = filteredData.map((row: any) => {
        return {
          clothingId: row['服装编号'] || '',
          seriesNumber: Number(row['包序']) || 0,
          clothingName: row['服装名称'] || '',
          style: row['款式'] || '',
          packageQuantity: Number(row['包裹数量']) || 0,
          QUP: Number(row['每包数量']) || 0,
          outPcs: Number(row['出货数量']) || 0,
          oem: row['是否OEM'] || '否',
        }
      })

      // 更新表单数据
      form.details = details

      ElMessage.success(`成功导入 ${details.length} 条数据`)
    } catch (error) {
      console.error('解析Excel失败:', error)
      ElMessage.error('解析Excel失败，请检查文件格式')
    }
  }
  reader.readAsArrayBuffer(file.raw)
}

// 获取服装信息
async function getClothingInfo() {
  // 检查服装年份是否已设置
  if (!clothingYear.value) {
    ElMessage.warning('请先设置服装年份')
    return
  }

  try {
    // 如果是新增模式，设置新的发货编码
    if (props.type === 'add') {
      // 1. 获取最新的发货编码
      let newTransportationId = ''
      try {
        const latestResponse = await getLatestTransportationId()

        // 使用类型断言处理响应数据
        const responseData = latestResponse as any
        let transportationId = ''

        // 尝试从不同的响应格式中提取数据
        if (responseData && typeof responseData === 'object') {
          if (responseData.transportation_id) {
            transportationId = responseData.transportation_id
          } else if (responseData.data && responseData.data.transportation_id) {
            transportationId = responseData.data.transportation_id
          }
        }

        if (transportationId) {
          // 提取数字部分并加1
          const idMatch = transportationId.match(/FH(\d+)(\d{3})/)
          if (idMatch) {
            const yearPart = idMatch[1]
            const numPart = parseInt(idMatch[2]) + 1
            // 确保序号是3位数
            const newNumPart = numPart.toString().padStart(3, '0')
            newTransportationId = `FH${yearPart}${newNumPart}`
          } else {
            // 如果无法解析现有ID，则使用默认格式
            newTransportationId = `FH${form.year.replace(/[^0-9]/g, '')}001`
          }
        } else {
          // 如果没有获取到ID，则使用默认格式
          newTransportationId = `FH${form.year.replace(/[^0-9]/g, '')}001`
        }
      } catch (error) {
        console.error('获取最新发货编码失败:', error)
        // 使用默认格式
        newTransportationId = `FH${form.year.replace(/[^0-9]/g, '')}001`
      }

      form.code = newTransportationId
    }

    // 2. 分析明细数据，准备查询服装信息
    const normalClothingNames: string[] = []
    const oemClothingNames: string[] = []

    form.details.forEach((detail) => {
      if (!detail.clothingId) {
        if (detail.oem === '是') {
          //oemClothingNames数组 里不存在，push
          if (!oemClothingNames.includes(detail.clothingName)) {
            oemClothingNames.push(detail.clothingName)
          }
        } else {
          //normalClothingNames数组 里不存在，push
          if (!normalClothingNames.includes(detail.clothingName)) {
            normalClothingNames.push(detail.clothingName)
          }
        }
      }
    })

    // 3. 查询服装信息
    let clothingList: any[] = []
    let oemClothingList: any[] = []

    if (normalClothingNames.length > 0 || oemClothingNames.length > 0) {
      try {
        const response = await findClothingByNames({
          clothing_names: normalClothingNames,
          clothing_year: clothingYear.value.toString(),
        })
        console.log('查询到的服装信息:', response)
        const oemResponse = await findOemClothingByNames({
          oem_clothing_names: oemClothingNames,
          oem_clothing_year: clothingYear.value.toString(),
        })
        console.log('查询到的OEM服装信息:', oemResponse)

        // 处理API响应 - 使用类型断言处理响应数据
        const responseData = response as any
        const oemResponseData = oemResponse as any
        if (responseData) {
          clothingList = responseData.data.clothingList || []
          console.log('clothingList', clothingList)
        }
        if (oemResponseData) {
          oemClothingList = oemResponseData.data.oemClothingList || []
          console.log('oemClothingList', oemClothingList)
        }
      } catch (error) {
        console.error('查询服装信息失败:', error)
        ElMessage.error('查询服装信息失败')
      }
    }

    // 4. 补充服装信息
    if (form.details.length > 0) {
      form.details = form.details.map((detail) => {
        // 如果已有服装ID，则跳过
        if (detail.clothingId) {
          return detail
        }

        // 根据服装名称查找对应的服装信息
        if (detail.oem === '是') {
          const oemClothing = oemClothingList.find(
            (c: any) => c.oem_clothing_name === detail.clothingName
          )
          if (oemClothing) {
            detail.clothingId = oemClothing.oem_clothing_id
            detail.style = oemClothing.style
          }
        } else {
          const clothing = clothingList.find((c: any) => c.clothing_name === detail.clothingName)
          if (clothing) {
            detail.clothingId = clothing.clothing_id
            detail.style = clothing.style
          }
        }

        return detail
      })

      ElMessage.success('服装信息补齐完成')
    }

    // 5. 自动计算总数量
    const totalOutPcs = form.details.reduce((sum, detail) => sum + (detail.outPcs || 0), 0)
    form.totalPcs = totalOutPcs

    // 6. 自动计算包裹数量
    const totalPackages = form.details.reduce(
      (sum, detail) => sum + (detail.packageQuantity || 0),
      0
    )
    form.totalPackageQuantity = totalPackages

    // 7. 自动生成备注，格式为：总数：计算出来的总数
    form.remark = `总数：${totalOutPcs}`
  } catch (error) {
    console.error('获取服装信息失败', error)
    ElMessage.error('获取服装信息失败，请检查网络连接')
  }
}

// 处理年份变化，更新运输公司选项
async function handleYearChange(value: string) {
  console.log('年份变化:', value)
  try {
    // 根据选择的年份更新运输公司选项
    const supplierResponse = await getTransportationSupplierOptions(value)
    supplierOptions.value = supplierResponse.data.suppliers || []
    console.log('已更新运输公司选项:', supplierOptions.value)
  } catch (error) {
    console.error('更新运输公司选项失败:', error)
  }
}

// 关闭对话框
function handleClose() {
  // 使用emit更新visible属性，而不是直接修改dialogVisible
  emit('update:visible', false)
  resetForm()
}

// 处理图片上传和删除
async function processImages(): Promise<TransportationImage[]> {
  // 1. 删除标记为需要删除的图片
  if (imagesToDelete.value.length > 0) {
    try {
      console.log('删除服务器上的图片:', imagesToDelete.value)
      await deleteImages(imagesToDelete.value)
      ElMessage.success('已删除标记的图片')
    } catch (error) {
      console.error('删除图片失败:', error)
      ElMessage.error('部分图片删除失败，但会继续保存表单')
    }
  }

  // 2. 获取已有的图片（从服务器加载的，且未被标记删除）
  const existingImages = fileList.value
    .filter((file) => (file as any).customData)
    .map((file) => (file as any).customData as TransportationImage)

  // 3. 上传新选择的图片
  const newImages: TransportationImage[] = []
  if (pendingUploadFiles.value.length > 0) {
    ElMessage.info('正在上传图片，请稍候...')

    // 逐个上传图片
    for (const file of pendingUploadFiles.value) {
      try {
        const imageInfo = await uploadImage(file, UploadType.TRANSPORTATION)
        if (imageInfo) {
          newImages.push(imageInfo as TransportationImage)
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        ElMessage.error(`图片 ${file.name} 上传失败`)
        // 继续上传其他图片
      }
    }
  }

  // 4. 合并已有图片和新上传的图片
  return [...existingImages, ...newImages]
}

// 提交表单
async function handleSubmit() {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (!valid) {
      ElMessage.error('请完善表单信息')
      return
    }

    if (form.details.length === 0) {
      ElMessage.warning('请添加发货明细')
      return
    }

    // 提交表单
    emit('submit', {
      formData: {
        transportation_id: form.code,
        transportation_year: form.year,
        date_out: new Date(form.shipmentDate),
        date_arrived: form.arrivalDate ? new Date(form.arrivalDate) : null,
        supplier: form.supplier,
        total_package_quantity: form.totalPackageQuantity || null,
        total_pcs: form.totalPcs || null,
        weight: form.weight || null,
        price: form.price || null,
        shipping_method: form.shippingMethod,
        remark: form.remark,
        transportation_img: await processImages(),
      },
      details: form.details.map((detail) => ({
        transportation_id: form.code,
        series_number: detail.seriesNumber,
        clothing_name: detail.clothingName,
        //package_quantity取两位小数
        package_quantity: detail.packageQuantity?.toFixed(2),
        QUP: detail.QUP,
        out_pcs: detail.outPcs,
        oem: detail.oem,
        clothing_id: detail.clothingId,
        style: detail.style,
      })),
    })
  })
}
</script>

<style scoped>
.transportation-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.dialog-content-wrapper {
  position: relative;
  min-height: 200px;
  width: 100%;
}

.transportation-tabs {
  width: 100%;
}

.transportation-form {
  margin-bottom: 20px;
  max-height: 60vh;
  overflow-y: auto;
  padding-right: 10px;
}

.form-row {
  display: flex;
  gap: 20px;
}

.form-row .el-form-item {
  flex: 1;
}

.detail-table {
  margin-top: 20px;
  margin-bottom: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.button-row {
  display: flex;
  gap: 10px;
  margin-top: 10px;
  justify-content: space-between;
  align-items: center;
}

.clothing-year-input {
  margin-left: auto;
}

/* 图片容器样式 */
.image-container {
  display: flex;
  gap: 20px;
}

.image-preview-section {
  flex: 3;
}

.upload-section {
  flex: 2;
  min-width: 200px;
}

.image-preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-image-item {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
  cursor: pointer;
  object-fit: cover;
}

.image-error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  color: #909399;
}

.error-text {
  margin-top: 8px;
  font-size: 12px;
}

/* 覆盖 Element Plus 的图片预览样式 */
:deep(.el-image-viewer__wrapper) {
  z-index: 3000 !important;
}

:deep(.el-image-viewer__close) {
  color: #fff;
}

:deep(.el-image-viewer__actions) {
  opacity: 1;
}

:deep(.el-image-viewer__canvas) {
  user-select: none;
}
</style>
