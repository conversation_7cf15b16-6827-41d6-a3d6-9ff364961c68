<template>
  <el-dialog
    v-model="dialogVisible"
    width="90%"
    top="2vh"
    align-center
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="division-work-dialog"
    :lock-scroll="true"
    :append-to-body="true"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" style="margin: 0 auto">
      <div class="form-header">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="所属年份" prop="division_work_year">
              <el-select
                v-model="form.division_work_year"
                placeholder="选择年份"
                style="width: 100%"
                :disabled="type === 'edit' || type === 'settlement'"
              >
                <el-option v-for="year in yearOptions" :key="year" :label="year" :value="year" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="缝制组" prop="group_name">
              <el-select
                v-model="form.group_name"
                placeholder="选择缝制组"
                style="width: 100%"
                clearable
                :disabled="type === 'settlement'"
              >
                <el-option
                  v-for="group in groupOptions"
                  :key="group"
                  :label="group"
                  :value="group"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="服装名称" prop="clothing_name">
              <el-select
                v-model="form.clothing_name"
                placeholder="选择服装"
                style="width: 100%"
                filterable
                clearable
                @change="handleClothingNameChange"
                :disabled="type === 'settlement'"
              >
                <el-option
                  v-for="clothing in clothingOptions"
                  :key="clothing.clothing_id"
                  :label="clothing.clothing_name"
                  :value="clothing.clothing_name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="服装编号" prop="clothing_id">
              <el-input
                v-model="form.clothing_id"
                placeholder="输入服装编号"
                @blur="handleClothingIdBlur"
                :disabled="true"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="缝制数" prop="pcs">
              <el-input v-model.number="form.pcs" placeholder="输入缝制数" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工艺细节" prop="craft_details">
              <el-input
                v-model="form.craft_details"
                placeholder="输入工艺细节"
                :rows="1"
                :disabled="type === 'settlement'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服装信息">
              <div class="clothing-info-container" v-loading="loadingClothingInfo">
                <div v-if="currentClothingInfo" class="clothing-info">
                  <el-text>
                    {{ currentClothingInfo.supplier || '无' }} |
                    {{ currentClothingInfo.group_classification?.join(', ') || '无' }} |
                    {{ currentClothingInfo.long_or_short_sleeve || '无' }} |
                    {{ currentClothingInfo.pocket_type || '无' }} |
                    {{ currentClothingInfo.style || '无' }} |
                    {{ currentClothingInfo.size || '无' }}
                  </el-text>
                </div>
                <div v-else-if="!loadingClothingInfo" class="no-clothing-info">
                  <el-text type="info">请先选择服装</el-text>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 隐藏的分组编号字段 -->
        <div style="display: none">
          <el-form-item label="分组编号" prop="division_work_id">
            <el-input
              v-model="form.division_work_id"
              placeholder="输入分组编号"
              :disabled="type === 'edit'"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 工序分配 -->
      <div class="work-assignment">
        <!-- 表格加载中状态 -->
        <div v-if="tableLoading" class="table-loading-container">
          <el-empty description="表格数据加载中..." :image-size="80">
            <template #image>
              <el-icon class="loading-icon"> </el-icon>
            </template>
          </el-empty>
        </div>

        <!-- 表格内容，仅在数据加载完成后显示 -->
        <el-table
          v-else
          :data="staffLines"
          size="small"
          style="font-size: 14px"
          border
          height="600px"
          :header-cell-style="{ 'text-align': 'center' }"
          @selection-change="handleAssignListSelectionChange"
          @sort-change="handleSortChange"
        >
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column type="index" label="序号" width="60" align="center" />
          <el-table-column
            label="员工姓名"
            align="center"
            width="120"
            sortable="custom"
            prop="staff"
          >
            <template #default="scope">
              <el-select
                v-model="scope.row.staff"
                placeholder="选择员工"
                filterable
                :filter-method="staffFilter"
                @blur="handleStaffBlur"
                @change="() => calculateTotalPrice(scope.$index)"
                value-key="staff_id"
              >
                <el-option
                  v-for="staff in filteredStaffOptions"
                  :key="staff.staff_id"
                  :label="staff.name"
                  :value="staff"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="工价合计" width="100" align="center">
            <template #default="scope">
              <el-tag>{{ scope.row.totalPrice }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="工序明细">
            <template #default="scope">
              <div class="work_detail">
                <div class="work_detail-wrapper">
                  <div
                    v-for="(detail, index) in scope.row.workDetails"
                    :key="index"
                    class="work_detail-item"
                  >
                    <el-icon @click="removeWorkDetail(scope.$index, index)" class="remove-icon"
                      ><Remove
                    /></el-icon>
                    <el-select
                      filterable
                      class="workSelect"
                      v-model="detail.work"
                      placeholder="选择工序"
                      :filter-method="workFilter"
                      :class="{
                        'inconsistent-work':
                          isStatisticsExecuted &&
                          detail.work &&
                          inconsistentWorkIds.includes(detail.work.work_id),
                      }"
                      @blur="handleWorkBlur"
                      @change="() => calculateTotalPrice(scope.$index)"
                      value-key="work_id"
                    >
                      <el-option
                        v-for="work in filteredWorkOptions"
                        :key="work.work_id"
                        :label="work.work_name"
                        :value="work"
                      />
                    </el-select>
                    <el-input
                      v-model="detail.pcs"
                      :style="{
                        width: type === 'settlement' ? '60px' : '45px',
                        fontSize: type === 'settlement' ? '12px' : '14px',
                      }"
                      :class="{
                        'inconsistent-work':
                          isStatisticsExecuted &&
                          detail.work &&
                          inconsistentWorkIds.includes(detail.work.work_id),
                      }"
                      align="right"
                      @change="() => calculateTotalPrice(scope.$index)"
                    />
                  </div>

                  <el-icon
                    color="#409eff"
                    size="22px"
                    @click="addWorkDetail(scope.$index)"
                    class="add-icon"
                  >
                    <Plus />
                  </el-icon>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer-buttonGroup">
          <el-button
            type="danger"
            size="small"
            @click="removeSelectedStaffLines"
            :disabled="selectedStaffIds.length === 0"
          >
            批量删除
          </el-button>
          <el-button type="primary" size="small" @click="addStaffLine">新增</el-button>
          <el-popover
            placement="top"
            :width="400"
            trigger="click"
            :visible="statisticsPopoverVisible"
            @hide="statisticsPopoverVisible = false"
          >
            <template #reference>
              <el-button type="warning" size="small" @click="handleStatistics">统计</el-button>
            </template>
            <div class="statistics-popover">
              <div class="statistics-header">
                <h3 class="statistics-title">工序统计结果</h3>
                <el-button
                  type="primary"
                  link
                  size="small"
                  @click="statisticsPopoverVisible = false"
                  class="close-button"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
              <div class="statistics-content">
                <div
                  v-for="item in statisticsArray"
                  :key="item.workId"
                  class="statistics-item"
                  :class="{ 'inconsistent-item': item.isInconsistent }"
                >
                  <span class="work-name">{{ item.workName }}</span>
                  <span class="work-pcs">{{ item.pcs }}</span>
                </div>
              </div>
              <div class="statistics-footer">
                <span>
                  <el-tag type="primary">工序总数: {{ totalWorkTypes }}</el-tag>
                </span>
                <span v-if="props.type === 'settlement'">
                  <el-tag type="info">裁剪数: {{ form.pcs }}</el-tag>
                </span>
                <span v-else>
                  <el-tag type="info">标准系数: 1</el-tag>
                </span>
                <span v-if="inconsistentWorkIds.length > 0">
                  <el-tag type="danger">不一致: {{ inconsistentWorkIds.length }}</el-tag>
                </span>
                <span v-else>
                  <el-tag type="success">全部一致</el-tag>
                </span>
              </div>
            </div>
          </el-popover>
          <div style="flex-grow: 1"></div>
          <el-button type="primary" size="small" v-if="type === 'settlement'" @click="calculatePcs">
            计算件数
          </el-button>
          <el-button type="primary" size="small" v-if="type === 'settlement'" @click="recoverPcs">
            恢复系数
          </el-button>
        </div>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ type === 'settlement' ? '结算' : '确定' }}
        </el-button>
        <el-button
          type="danger"
          v-if="type === 'settlement' && props.editData?.is_complete === 1"
          @click="handleCancelSettlement"
          :loading="cancelingSettlement"
        >
          取消结算
        </el-button>
        <DivisionWorkPrint
          v-if="type === 'edit'"
          :division-work="form"
          :assign-list="prepareAssignListForPrint()"
        />
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, defineProps, defineEmits, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox, ElPopover, ElLoading } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { getDivisionWorkYearOptions, getDivisionWorkGroupOptions } from '@/api/divisionWork'
import { getClothingById, getClothingList, updateClothing } from '@/api/clothing'
import { getStaffList } from '@/api/staff'
import { getWorkList } from '@/api/work'
import type { DivisionWork } from '@/api/divisionWork'
import type { Staff } from '@/api/staff'
import type { Work } from '@/types/work'
import type { Clothing } from '@/types/clothing'
import { getDivisionWorkAssignByDivisionWorkId } from '@/api/divisionWorkAssign'
import { Plus, Remove, Close } from '@element-plus/icons-vue'
import { Lunar } from 'lunar-typescript'
import PinYinMatch from 'pinyin-match'
import DivisionWorkPrint from './DivisionWorkPrint.vue'

// 定义工序明细接口
interface WorkDetail {
  work: Work | null
  pcs: number
}

// 定义员工工序行接口
interface StaffLine {
  staff: Staff | null
  totalPrice: number
  workDetails: WorkDetail[]
}

// 定义提交数据接口
interface SubmitWorkDetail {
  work_id: string
  work_name: string
  pcs: number
}

// 定义提交的分配项接口
interface SubmitAssignItem {
  division_work_id: string
  staff_id: string
  staff_name: string
  totalPrice: string // 修改为string类型以匹配后端期望
  work_detail: SubmitWorkDetail[]
}

// 定义提交表单数据接口
interface SubmitFormData extends DivisionWork {
  assign_list: SubmitAssignItem[]
}

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String as () => 'add' | 'edit' | 'settlement',
    default: 'add',
  },
  editData: {
    type: Object as () => DivisionWork | null,
    default: null,
  },
  // 复制的分工分配数据
  copiedAssignData: {
    type: Array,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'cancel', 'cancelSettlement'])

// 表单引用
const formRef = ref<FormInstance>()
// 对话框可见性
const dialogVisible = ref(false)
// 提交中状态
const submitting = ref(false)
// 取消结算中状态
const cancelingSettlement = ref(false)
// 表格数据加载状态
const tableLoading = ref(false)
// 年份选项
const yearOptions = ref<string[]>([])
// 缝制组选项
const groupOptions = ref<string[]>([])
// 员工选项
const staffOptions = ref<Staff[]>([])
// 工序选项
const workOptions = ref<Work[]>([])
// 服装选项
const clothingOptions = ref<Clothing[]>([])
// 员工工序行
const staffLines = ref<StaffLine[]>([])
// 服装信息加载状态
const loadingClothingInfo = ref(false)
// 当前服装信息
const currentClothingInfo = ref<Clothing | null>(null)
// 选中的员工ID数组
const selectedStaffIds = ref<string[]>([])
// 是否已计算件数（用于重置计算件数标志）
const isCalculated = ref(false)
// 工序统计结果
const workStatistics = ref<Map<string, number>>(new Map())
// 工序统计是否已执行
const isStatisticsExecuted = ref(false)
// Popover 是否可见
const statisticsPopoverVisible = ref(false)
// 不一致的工序ID列表
const inconsistentWorkIds = ref<string[]>([])

// 统计结果数组，用于显示在Popover中
const statisticsArray = computed(() => {
  const result: { workId: string; workName: string; pcs: number; isInconsistent: boolean }[] = []

  workStatistics.value.forEach((pcs, workId) => {
    const work = workOptions.value.find((w) => w.work_id === workId)
    if (work) {
      result.push({
        workId,
        workName: work.work_name,
        pcs,
        isInconsistent: inconsistentWorkIds.value.includes(workId),
      })
    }
  })

  // 按工序名称排序
  return result.sort((a, b) => a.workName.localeCompare(b.workName))
})

// 工序总数
const totalWorkTypes = computed(() => {
  return workStatistics.value.size
})
// 员工选项备份
const copyStaffOptions = ref<Staff[]>([])
// 工序选项备份
const copyWorkOptions = ref<Work[]>([])
// 过滤后的员工选项
const filteredStaffOptions = ref<Staff[]>([])
// 过滤后的工序选项
const filteredWorkOptions = ref<Work[]>([])

// 表单数据
const form = reactive({
  division_work_year: '',
  division_work_id: '',
  clothing_id: '',
  clothing_name: '',
  group_name: '',
  pcs: 0,
  is_complete: 0,
  craft_details: '',
})

// 表单验证规则
const rules = reactive<FormRules>({
  division_work_year: [{ required: true, message: '请选择所属年份', trigger: 'change' }],
  division_work_id: [{ required: true, message: '请输入分组编号', trigger: 'blur' }],
  clothing_id: [{ required: true, message: '请输入服装编号', trigger: 'blur' }],
  clothing_name: [{ required: true, message: '', trigger: 'change' }],
  pcs: [
    { required: false, message: '请输入件数', trigger: 'blur' },
    { type: 'number', message: '件数必须为数字', trigger: 'blur' },
  ],
  is_complete: [{ required: false, message: '请选择状态', trigger: 'change' }],
  craft_details: [{ required: false, message: '请输入工艺细节', trigger: 'blur' }],
})

// 监听visible属性变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val
    if (val) {
      // 当对话框打开时，只加载服装数据
      // 员工和工序数据将在需要时懒加载
      loadClothingOptions()
    }
  }
)

// 监听dialogVisible变化
watch(
  () => dialogVisible.value,
  (val) => {
    emit('update:visible', val)
  }
)

// 生成分组编号
const generateDivisionWorkId = () => {
  const now = new Date()
  const prefix = 'FG'
  const timestamp = now.getTime()
  return `${prefix}${timestamp}`
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  form.division_work_year = yearOptions.value[0] || ''
  form.division_work_id = generateDivisionWorkId()
  form.clothing_id = ''
  form.clothing_name = ''
  form.group_name = ''
  form.pcs = 0
  form.is_complete = 0
  form.craft_details = ''

  // 初始化员工工序行
  initStaffLines()
}

// 初始化员工工序行
const initStaffLines = () => {
  staffLines.value = [createDefaultStaffLine()]
}

// 创建默认员工工序行
const createDefaultStaffLine = (): StaffLine => {
  return {
    staff: null,
    totalPrice: 0,
    workDetails: [createDefaultWorkDetail()],
  }
}

// 创建默认工序明细
const createDefaultWorkDetail = (): WorkDetail => {
  return {
    work: null,
    pcs: 1, // 默认数量为1
  }
}

// 添加员工行
const addStaffLine = () => {
  staffLines.value.push(createDefaultStaffLine())
}

// 移除选中的员工行
const removeSelectedStaffLines = () => {
  if (selectedStaffIds.value.length > 0) {
    staffLines.value = staffLines.value.filter((line) => {
      return !selectedStaffIds.value.includes(line.staff?.staff_id || '')
    })
  }
}

// 添加工序明细
const addWorkDetail = (staffIndex: number) => {
  staffLines.value[staffIndex].workDetails.push(createDefaultWorkDetail())
}

// 移除工序明细
const removeWorkDetail = (staffIndex: number, workIndex: number) => {
  if (staffLines.value[staffIndex].workDetails.length > 1) {
    staffLines.value[staffIndex].workDetails.splice(workIndex, 1)
    calculateTotalPrice(staffIndex)
  }
}

// 工序变更处理已通过 @change 事件直接调用 calculateTotalPrice

// 计算员工工价合计
const calculateTotalPrice = (staffIndex: number) => {
  const staffLine = staffLines.value[staffIndex]
  let total = 0

  staffLine.workDetails.forEach((detail) => {
    if (detail.work && detail.pcs) {
      // 获取当前年份的工价
      const yearlyPrice = detail.work.yearly_prices?.find(
        (yp) => yp.year === form.division_work_year
      )
      const price = yearlyPrice ? yearlyPrice.price : detail.work.work_price
      total += (price * detail.pcs) / 100
    }
  })

  if (props.type === 'edit') {
    staffLine.totalPrice = parseFloat(total.toFixed(3))
  } else {
    staffLine.totalPrice = parseFloat(total.toFixed(3))
  }
}

// 处理分工明细列表多选
const handleAssignListSelectionChange = (selection: StaffLine[]) => {
  selectedStaffIds.value = selection
    .map((item) => item.staff?.staff_id || '')
    .filter((id) => id !== '')
}

// 员工数据缓存
const staffCache = ref<{
  data: Staff[]
  timestamp: number
  loaded: boolean
}>({
  data: [],
  timestamp: 0,
  loaded: false
})

// 缓存过期时间（毫秒）
const CACHE_EXPIRY = 5 * 60 * 1000 // 5分钟

// 加载员工选项
const loadStaffOptions = async () => {
  try {
    // 检查缓存是否有效
    const now = Date.now()
    if (
      staffCache.value.loaded &&
      staffCache.value.data.length > 0 &&
      now - staffCache.value.timestamp < CACHE_EXPIRY
    ) {
      console.log('使用缓存的员工数据，缓存时间：', new Date(staffCache.value.timestamp).toLocaleTimeString())
      staffOptions.value = staffCache.value.data
      copyStaffOptions.value = [...staffCache.value.data]
      filteredStaffOptions.value = [...staffCache.value.data]
      return
    }

    // 显示加载状态
    const loadingInstance = ElLoading.service({
      text: '加载员工数据中...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    const res = await getStaffList({
      limit: 200, // 减少获取的数据量，只获取最近的200条记录
    })

    if (res && res.data) {
      // 使用正确的类型处理API响应
      const staffData = res.data as { staffList?: Staff[] }
      if (staffData && staffData.staffList) {
        // 更新缓存
        staffCache.value = {
          data: staffData.staffList,
          timestamp: now,
          loaded: true
        }

        staffOptions.value = staffData.staffList
        copyStaffOptions.value = [...staffData.staffList]
        filteredStaffOptions.value = [...staffData.staffList]
      }
    }

    // 关闭加载状态
    loadingInstance.close()
  } catch (error) {
    ElMessage.error('加载员工数据失败')
    console.error('加载员工数据失败:', error)
  }
}

// 员工拼音筛选
const staffFilter = (query: string) => {
  if (query) {
    let result: Staff[] = []
    copyStaffOptions.value.forEach((staff) => {
      let matchResult = PinYinMatch.match(staff.name, query)
      if (matchResult) {
        result.push(staff)
      }
    })
    filteredStaffOptions.value = result
  } else {
    filteredStaffOptions.value = copyStaffOptions.value
  }
}

// 员工筛选失去焦点
const handleStaffBlur = () => {
  setTimeout(() => {
    filteredStaffOptions.value = copyStaffOptions.value
  }, 200)
}

// 处理表格排序变化
const handleSortChange = (column: { prop: string; order: string | null }) => {
  if (column.prop === 'staff' && column.order) {
    // 根据排序方向对员工进行排序
    staffLines.value.sort((a, b) => {
      const nameA = a.staff?.name || ''
      const nameB = b.staff?.name || ''
      return column.order === 'ascending' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA)
    })
  }
}

// 工序数据缓存
const workCache = ref<{
  data: Work[]
  timestamp: number
  loaded: boolean
  year: string
}>({
  data: [],
  timestamp: 0,
  loaded: false,
  year: ''
})

// 加载工序选项
const loadWorkOptions = async () => {
  try {
    const lunar = Lunar.fromDate(new Date())
    const lunarYear = lunar.getYear() + '年'

    // 检查缓存是否有效
    const now = Date.now()
    if (
      workCache.value.loaded &&
      workCache.value.data.length > 0 &&
      now - workCache.value.timestamp < CACHE_EXPIRY &&
      workCache.value.year === lunarYear
    ) {
      console.log('使用缓存的工序数据，缓存时间：', new Date(workCache.value.timestamp).toLocaleTimeString())
      workOptions.value = workCache.value.data
      copyWorkOptions.value = [...workCache.value.data]
      filteredWorkOptions.value = [...workCache.value.data]
      return
    }

    // 显示加载状态
    const loadingInstance = ElLoading.service({
      text: '加载工序数据中...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    // 查询条件：获取当前年份的工序
    const res = await getWorkList({
      years: [lunarYear], // 当前农历年份
      limit: 500, // 限制数据量
    })

    if (res && res.data) {
      // 使用正确的类型处理API响应
      const workData = res.data as { workList?: Work[] }
      if (workData && workData.workList) {
        // 更新缓存
        workCache.value = {
          data: workData.workList,
          timestamp: now,
          loaded: true,
          year: lunarYear
        }

        workOptions.value = workData.workList
        copyWorkOptions.value = [...workData.workList]
        filteredWorkOptions.value = [...workData.workList]
      }
    }

    // 关闭加载状态
    loadingInstance.close()
  } catch (error) {
    ElMessage.error('加载工序数据失败')
    console.error('加载工序数据失败:', error)
  }
}

// 工序拼音筛选
const workFilter = (query: string) => {
  if (query) {
    let result: Work[] = []
    copyWorkOptions.value.forEach((work) => {
      let matchResult = PinYinMatch.match(work.work_name, query)
      if (matchResult) {
        result.push(work)
      }
    })
    filteredWorkOptions.value = result
  } else {
    filteredWorkOptions.value = copyWorkOptions.value
  }
}

// 工序选项失去焦点
const handleWorkBlur = () => {
  setTimeout(() => {
    filteredWorkOptions.value = copyWorkOptions.value
  }, 200)
}

// 不再需要单独处理数量输入，直接通过 @change 事件调用 calculateTotalPrice

// 根据系数计算件数
const calculatePcs = () => {
  if (form.pcs > 0) {
    staffLines.value.forEach((staffLine) => {
      let totalPrice = 0
      staffLine.workDetails.forEach((detail) => {
        if (detail.work && detail.pcs) {
          detail.pcs = parseInt((form.pcs * detail.pcs).toString())

          // 获取当前年份的工价
          const yearlyPrice = detail.work.yearly_prices?.find(
            (yp) => yp.year === form.division_work_year
          )
          const price = yearlyPrice ? yearlyPrice.price : detail.work.work_price
          totalPrice += (price * detail.pcs) / 100
        }
      })
      staffLine.totalPrice = parseFloat(totalPrice.toFixed(0))
    })
  } else {
    ElMessage.error('请输入缝制数')
  }
}

// 恢复系数
const recoverPcs = () => {
  if (form.pcs > 0) {
    staffLines.value.forEach((staffLine) => {
      let totalPrice = 0
      staffLine.workDetails.forEach((detail) => {
        if (detail.work && detail.pcs) {
          detail.pcs = detail.pcs / form.pcs

          // 获取当前年份的工价
          const yearlyPrice = detail.work.yearly_prices?.find(
            (yp) => yp.year === form.division_work_year
          )
          const price = yearlyPrice ? yearlyPrice.price : detail.work.work_price
          totalPrice += (price * detail.pcs) / 100
        }
      })
      staffLine.totalPrice = parseFloat(totalPrice.toFixed(3))
    })
  } else {
    ElMessage.error('请输入缝制数')
  }
}

// 加载服装选项
const loadClothingOptions = async () => {
  try {
    const lunar = Lunar.fromDate(new Date())
    const lunarYear = lunar.getYear() + '年'

    // 使用正确的类型
    const params: { limit: number; make_year?: string } = {
      limit: 9999, // 不分页，获取全部数据
    }

    // 添加make_year参数
    params.make_year = lunarYear

    const res = await getClothingList(params)

    if (res && res.data) {
      // 使用正确的类型处理API响应
      const clothingData = res.data as { clothingList?: Clothing[] }
      if (clothingData && clothingData.clothingList) {
        clothingOptions.value = clothingData.clothingList
      }
    }
  } catch (error) {
    ElMessage.error('加载服装数据失败')
  }
}

// 监听editData变化
watch(
  () => props.editData,
  (val) => {
    if (val) {
      // 编辑模式，填充表单数据
      Object.keys(form).forEach((key) => {
        if (key in val) {
          // @ts-ignore
          form[key] = val[key]
        }
      })

      // 在编辑模式下，根据服装编号获取服装信息
      if (form.clothing_id) {
        fetchClothingInfo(form.clothing_id)
      }

      // 如果是复制模式（新增模式但有editData）且有复制的分工分配数据
      if (props.type === 'add' && props.copiedAssignData && props.copiedAssignData.length > 0) {
        // 使用复制的分工分配数据
        loadCopiedAssignData()
        console.log('复制的分工分配数据:', props.copiedAssignData)
        form.division_work_year = Lunar.fromDate(new Date()).getYear() + '年'
        // 不获取服装信息，因为我们不保留服装相关字段
        currentClothingInfo.value = null
      }
      // 如果是结算模式，加载分工分配数据
      else if (props.type === 'settlement' && val.division_work_id) {
        loadCopiedAssignData(true) // 传入true表示是结算模式
        // 在结算模式下，保留服装信息
        if (form.clothing_id) {
          fetchClothingInfo(form.clothing_id)
        }
      }
      // 否则，如果是编辑模式，通过API获取工序分配数据
      else if (props.type === 'edit' && val.division_work_id) {
        loadAssignListFromApi(val.division_work_id)
      } else {
        initStaffLines()
      }
    } else {
      // 新增模式，重置表单
      resetForm()
      // 清空服装信息
      currentClothingInfo.value = null
    }
  },
  { immediate: true }
)

// 加载复制的分工分配数据
// isSettlement 参数用于标记是否为结算模式，在调用时传入
const loadCopiedAssignData = async (_isSettlement = false) => {
  try {
    // 先初始化空行，避免显示旧数据
    initStaffLines()

    // 设置表格加载状态
    tableLoading.value = true

    // 确保员工和工序数据已加载
    if (staffOptions.value.length === 0 || workOptions.value.length === 0) {
      await Promise.all([
        staffOptions.value.length === 0 ? loadStaffOptions() : Promise.resolve(),
        workOptions.value.length === 0 ? loadWorkOptions() : Promise.resolve(),
      ])
    }

    // 使用复制的分工分配数据
    if (props.copiedAssignData && props.copiedAssignData.length > 0) {
      staffLines.value = props.copiedAssignData.map((item: any) => {
        return {
          staff: staffOptions.value.find((s) => s.staff_id === item.staff_id) || null,
          totalPrice: parseFloat(item.totalPrice || '0'),
          workDetails: item.work_detail.map((detail: any) => {
            return {
              work: workOptions.value.find((w) => w.work_id === detail.work_id) || null,
              pcs: detail.pcs || 1,
            }
          }),
        }
      })
    }
  } catch (error) {
    ElMessage.error('加载复制的分工分配数据失败')
    console.error('加载复制的分工分配数据失败:', error)
  } finally {
    tableLoading.value = false
  }
}

// 从API加载工序分配数据
const loadAssignListFromApi = async (divisionWorkId: string) => {
  try {
    // 先初始化空行，避免显示旧数据
    initStaffLines()

    // 设置表格加载状态
    tableLoading.value = true

    const res = await getDivisionWorkAssignByDivisionWorkId(divisionWorkId)
    console.log('获取到的分工分配数据:', res)
    if (res && res.data) {
      const assignData = res.data
      if (Array.isArray(assignData) && assignData.length > 0) {
        // 确保员工和工序数据已加载
        if (staffOptions.value.length === 0 || workOptions.value.length === 0) {
          await Promise.all([
            staffOptions.value.length === 0 ? loadStaffOptions() : Promise.resolve(),
            workOptions.value.length === 0 ? loadWorkOptions() : Promise.resolve(),
          ])
        }

        staffLines.value = assignData.map((item: any) => {
          return {
            staff: staffOptions.value.find((s) => s.staff_id === item.staff_id) || null,
            totalPrice: parseFloat(item.totalPrice || '0'),
            workDetails: item.work_detail.map((detail: any) => {
              return {
                work: workOptions.value.find((w) => w.work_id === detail.work_id) || null,
                pcs: detail.pcs || 1,
              }
            }),
          }
        })
      } else {
      }
    }
  } catch (error) {
    ElMessage.error('加载工序分配数据失败')
  } finally {
    tableLoading.value = false
  }
}

// 根据服装ID获取服装信息
const fetchClothingInfo = async (clothingId: string) => {
  if (!clothingId) {
    currentClothingInfo.value = null
    return
  }

  loadingClothingInfo.value = true

  try {
    const res = await getClothingById(clothingId)
    if (res && res.data) {
      // 使用类型断言处理API响应
      const clothingData = res.data as any
      // 更新当前服装信息
      currentClothingInfo.value = clothingData

      // 如果是新增模式，自动填充年份
      if (props.type === 'add' && !form.division_work_year && clothingData.clothing_year) {
        form.division_work_year = clothingData.clothing_year
      }

      // 填充工艺细节
      if (clothingData.craft_details) {
        form.craft_details = clothingData.craft_details
      } else {
        form.craft_details = ''
      }
    } else {
      currentClothingInfo.value = null
    }
  } catch (error) {
    ElMessage.error('查询服装信息失败')
    currentClothingInfo.value = null
  } finally {
    loadingClothingInfo.value = false
  }
}

// 服装编号失去焦点时不再查询服装信息
const handleClothingIdBlur = () => {
  // 仅在编辑模式下保留服装编号验证逻辑，不再主动查询服装信息
  if (!form.clothing_id) {
    // 如果清空了服装编号，也清空服装信息
    currentClothingInfo.value = null
  }
}

// 服装名称变更处理
const handleClothingNameChange = () => {
  // 根据选择的服装名称查找对应的服装信息
  const selectedClothing = clothingOptions.value.find((c) => c.clothing_name === form.clothing_name)
  if (selectedClothing) {
    // 填充服装编号
    form.clothing_id = selectedClothing.clothing_id

    // 从服装列表中获取的数据可能不完整，需要通过API获取完整信息
    fetchClothingInfo(selectedClothing.clothing_id)
  } else {
    currentClothingInfo.value = null
  }
}

// 准备提交数据
const prepareSubmitData = (): SubmitFormData => {
  // 基本表单数据
  const submitData = { ...form } as SubmitFormData

  // 添加工序分配数据
  const assignList = staffLines.value
    .map((line) => {
      if (!line.staff) return null

      // 确保工序明细中至少有一个有效的工序
      const validWorkDetails = line.workDetails
        .map((detail) => {
          if (!detail.work) return null

          return {
            work_id: detail.work.work_id,
            work_name: detail.work.work_name,
            pcs: detail.pcs,
          } as SubmitWorkDetail
        })
        .filter((item): item is SubmitWorkDetail => item !== null)

      // 如果没有有效的工序明细，则跳过这个员工
      if (validWorkDetails.length === 0) return null

      return {
        division_work_id: form.division_work_id,
        division_work_year: form.division_work_year,
        staff_id: line.staff.staff_id,
        staff_name: line.staff.name,
        totalPrice: line.totalPrice.toString(), // 转换为字符串类型
        work_detail: validWorkDetails,
      } as SubmitAssignItem
    })
    .filter((item): item is SubmitAssignItem => item !== null)

  // 添加工序分配数据到提交数据
  submitData.assign_list = assignList

  return submitData
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      // 验证员工分工信息
      if (
        staffLines.value.length === 0 ||
        !staffLines.value.some(
          (line) => line.staff && line.workDetails.some((detail) => detail.work)
        )
      ) {
        ElMessage.warning('请至少添加一名员工和工序')
        return
      }

      submitting.value = true
      try {
        // 准备提交数据
        const submitData = prepareSubmitData()

        // 如果是编辑模式，确保craft_details字段被保存
        if (currentClothingInfo.value) {
          // 更新服装的工艺细节
          try {
            if (currentClothingInfo.value._id && form.craft_details) {
              // 使用类型断言来处理类型问题，添加craft_details字段
              const updateData = {
                craft_details: form.craft_details,
              }

              await updateClothing(currentClothingInfo.value._id, updateData)
            }
          } catch (error) {
            ElMessage.warning('更新服装工艺细节失败，但将继续提交分工信息')
            // 继续提交分工信息，不阻止流程
          }
        }
        // 提交表单数据
        emit('submit', submitData)
      } catch (error) {
        ElMessage.error('提交失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 取消
const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
  // 重置计算件数标志
  isCalculated.value = false
  // 重置统计状态
  isStatisticsExecuted.value = false
  workStatistics.value.clear()
  inconsistentWorkIds.value = []
  // 清空表单数据，确保下次打开时是空白的
  if (props.type === 'add') {
    setTimeout(() => {
      resetForm()
      currentClothingInfo.value = null
    }, 300)
  }
}

// 取消结算
const handleCancelSettlement = () => {
  if (!props.editData || !props.editData.division_work_id) {
    ElMessage.warning('无法获取分工ID，取消结算失败')
    return
  }

  // 显示确认对话框
  ElMessageBox.confirm('确定要取消结算吗？这将删除所有相关的结算数据，且不可恢复！', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        cancelingSettlement.value = true
        // 触发取消结算事件，由父组件处理实际的API调用
        emit('cancelSettlement', props.editData?.division_work_id || '')
        // 关闭对话框
        dialogVisible.value = false
      } catch (error: any) {
        console.error('取消结算失败:', error)
        ElMessage.error(`取消结算失败: ${error.message || '未知错误'}`)
      } finally {
        cancelingSettlement.value = false
      }
    })
    .catch(() => {
      // 用户取消操作，不做任何处理
    })
}

// 关闭对话框
const handleClose = () => {
  handleCancel()
}

// 统计工序
const handleStatistics = () => {
  // 清空之前的统计结果
  workStatistics.value.clear()
  inconsistentWorkIds.value = []

  // 遍历所有员工的工序明细，统计每个工序的总数量
  staffLines.value.forEach((staffLine) => {
    staffLine.workDetails.forEach((detail) => {
      if (detail.work && detail.work.work_id) {
        const workId = detail.work.work_id
        const currentTotal = workStatistics.value.get(workId) || 0
        workStatistics.value.set(workId, currentTotal + detail.pcs)
      }
    })
  })

  // 根据不同模式进行比较
  if (props.type === 'settlement') {
    // 结算模式：与裁剪数（form.pcs）比较
    if (form.pcs > 0) {
      workStatistics.value.forEach((total, workId) => {
        if (Math.abs(total - form.pcs) > 0.001) {
          // 使用小数点容差比较
          inconsistentWorkIds.value.push(workId)
        }
      })
    }
  } else {
    // 编辑和新增模式：与1比较
    workStatistics.value.forEach((total, workId) => {
      if (Math.abs(total - 1) > 0.001) {
        // 使用小数点容差比较
        inconsistentWorkIds.value.push(workId)
      }
    })
  }

  // 设置统计已执行标志
  isStatisticsExecuted.value = true

  // 显示统计弹出框
  statisticsPopoverVisible.value = true

  // 显示统计结果
  if (inconsistentWorkIds.value.length > 0) {
    ElMessage.warning(`发现 ${inconsistentWorkIds.value.length} 个工序的数量不一致，已用颜色标记`)
  } else {
    ElMessage.success('所有工序数量一致')
  }
}

// 准备打印数据
const prepareAssignListForPrint = () => {
  return staffLines.value
    .filter((line) => line.staff !== null)
    .map((line) => {
      // 过滤有效的工序明细
      const validWorkDetails = line.workDetails
        .filter((detail) => detail.work !== null)
        .map((detail) => ({
          work_id: detail.work!.work_id,
          work_name: detail.work!.work_name,
          pcs: detail.pcs,
        }))

      return {
        staff_id: line.staff!.staff_id,
        staff_name: line.staff!.name,
        totalPrice: line.totalPrice.toString(),
        work_detail: validWorkDetails,
      }
    })
}

// 初始化
onMounted(async () => {
  try {
    // 获取年份选项
    const yearRes = await getDivisionWorkYearOptions()
    if (yearRes && yearRes.data) {
      // 使用正确的类型处理API响应
      const yearData = yearRes.data as { years?: string[] }
      if (yearData && yearData.years) {
        yearOptions.value = yearData.years
      }
    }

    // 获取缝制组选项
    const groupRes = await getDivisionWorkGroupOptions()
    if (groupRes && groupRes.data) {
      // 使用正确的类型处理API响应
      const groupData = groupRes.data as { groups?: string[] }
      if (groupData && groupData.groups) {
        groupOptions.value = groupData.groups
      }
    }

    // 如果是新增模式，初始化表单
    if (props.type === 'add' && !props.editData) {
      form.division_work_year = yearOptions.value[0] || ''
      form.division_work_id = generateDivisionWorkId()
    }

    // 初始化员工工序行
    initStaffLines()
  } catch (error) {
    ElMessage.error('初始化失败')
  }
})
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.table-footer-buttonGroup {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.form-header {
  margin-bottom: 20px;
}

.clothing-info-container {
  min-height: 40px;
}

.clothing-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.no-clothing-info {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-style: italic;
}

.work-assignment {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f5f7fa;
}

.work_detail {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  align-items: center; /* 垂直居中所有子元素 */
  min-height: 40px; /* 确保有足够的高度以便垂直居中 */
}

.work_detail-wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  align-items: center; /* 垂直居中所有子元素 */
  min-height: 40px; /* 确保有足够的高度 */
  justify-content: flex-start; /* 水平方向左对齐 */
}

.work_detail-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  height: 32px; /* 固定高度，与Element Plus的小号输入框高度一致 */
  width: 240px; /* 固定宽度 */
}

.workSelect {
  width: 160px;
  margin-left: 0; /* 移除左边距，让选择器与减号图标更靠近 */
  margin-right: 5px; /* 保留右边距 */
}

.table-footer-buttonGroup {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.remove-icon {
  cursor: pointer;
  margin-right: 2px; /* 减小右边距，让图标与选择器更靠近 */
  font-size: 16px;
  display: flex;
  align-items: center;
}

.add-icon {
  cursor: pointer;
  display: flex;
  align-items: center;
  margin-left: 5px;
}

/* 设置弹窗高度并防止页面滚动条 */
:deep(.division-work-dialog) {
  margin-top: 0vh !important; /* 减小顶部边距，让弹窗更高 */
}

/* 当弹窗打开时，禁用body滚动 */
:deep(body.el-popup-parent--hidden) {
  overflow: hidden !important;
  position: static !important;
  width: auto !important;
  padding-right: 0 !important;
}

:deep(.division-work-dialog .el-dialog) {
  height: 90vh !important; /* 缩小弹窗高度 */
  margin-bottom: 0 !important; /* 减小底部边距 */
  overflow: hidden !important; /* 防止内容溢出 */
  position: relative !important; /* 确保定位正确 */
  max-height: 90vh !important; /* 限制最大高度 */
}

:deep(.division-work-dialog .el-dialog__body) {
  height: 75vh !important; /* 减小内容区域高度 */
  overflow-y: auto;
  overflow-x: hidden !important; /* 防止水平滚动条 */
}

/* 设置表单项的margin为0 */
:deep(.el-form-item.asterisk-left.el-form-item--label-right) {
  margin-bottom: 10px !important;
}

/* 设置表格高度 */
:deep(.division-work-dialog .el-table) {
  height: 70vh !important; /* 减小表格高度 */
}

/* 加载状态样式 */
.table-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 600px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}

.loading-icon {
  font-size: 48px;
  color: #409eff;
  animation: loading-rotate 2s linear infinite;
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 不一致工序的样式 */
:deep(.inconsistent-work) {
  border-color: #f56c6c !important;
  background-color: #fef0f0 !important;
}

:deep(.inconsistent-work .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset !important;
  background-color: #fef0f0 !important;
}

:deep(.inconsistent-work .el-select__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c inset !important;
  background-color: #fef0f0 !important;
}

/* 统计弹出框样式 */
.statistics-popover {
  padding: 5px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.statistics-title {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.close-button {
  padding: 2px;
  margin: 0;
}

.statistics-content {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 10px;
  justify-content: space-between; /* 确保三列均匀分布 */
}

.statistics-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: calc(33.33% - 4px); /* 确保三列布局 */
  padding: 5px 8px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  box-sizing: border-box; /* 确保边框不会增加宽度 */
  margin-bottom: 4px; /* 增加底部间距 */
}

.inconsistent-item {
  background-color: #fef0f0;
  border-color: #f56c6c;
}

.work-name {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70%;
}

.work-pcs {
  font-size: 12px;
  font-weight: bold;
  color: #303133;
}

.statistics-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #e4e7ed;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
